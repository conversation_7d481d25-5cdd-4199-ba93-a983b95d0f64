#!/bin/bash
# 文件名: chaosblade_resource_manager.sh (重构版本)
# 功能: 基于 ChaosBlade 的自动资源负载管理脚本
# 用法: ./chaosblade_resource_manager.sh {start|stop|status|config|monitor|help}
# 重构说明: 消除了重复代码，统一了主程序和守护程序逻辑

# 脚本配置
SCRIPT_NAME="chaosblade_resource_manager"
PROGRAM_DIR="/opt/chaosblade-resource-manager"
SCRIPT_DIR="${PROGRAM_DIR}/data"
LOG_FILE="${SCRIPT_DIR}/resource_manager.log"
CONFIG_FILE="${SCRIPT_DIR}/config.conf"
EXPERIMENT_FILE="${SCRIPT_DIR}/experiments.txt"
PID_FILE="${SCRIPT_DIR}/daemon.pid"

# ChaosBlade 配置
CHAOSBLADE_DIR="/opt/chaosblade"
BLADE_BIN="${CHAOSBLADE_DIR}/blade"

# 创建程序目录
mkdir -p "$PROGRAM_DIR"

# 创建工作目录（如果已存在则清理重建）
# 注意：守护进程模式下不清理工作目录，避免删除主进程创建的文件
if [[ "$1" != "--daemon-mode" ]]; then
    if [[ -d "$SCRIPT_DIR" ]]; then
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] 清理已存在的工作目录: $SCRIPT_DIR"
        rm -rf "$SCRIPT_DIR"
    fi
fi
mkdir -p "$SCRIPT_DIR"

# 检查目录权限
if [[ ! -w "$PROGRAM_DIR" ]]; then
    echo "错误: 无法写入程序目录 $PROGRAM_DIR，请以root权限运行或手动创建目录"
    exit 1
fi

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date "+%Y-%m-%d %H:%M:%S")
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_message "WARNING" "建议以root权限运行此脚本以获得最佳效果"
    fi
}

# 生成随机数（从守护程序移入）
generate_random() {
    local min=$1
    local max=$2
    echo $(( RANDOM % (max - min + 1) + min ))
}

# ==================== 配置管理函数 ====================
# 创建默认配置
create_default_config() {
    log_message "INFO" "创建默认配置..."

    cat > "$CONFIG_FILE" << EOF
# 默认配置
CYCLE_RUN_TIME_MIN=18000
CYCLE_RUN_TIME_MAX=21600
CYCLE_STOP_TIME_MIN=60
CYCLE_STOP_TIME_MAX=180
CPU_PERCENT_MIN=40
CPU_PERCENT_MAX=50
MEMORY_PERCENT_MIN=50
MEMORY_PERCENT_MAX=60
ALLOCATION_TIME=$(date)
EOF

    log_message "INFO" "配置创建完成:"
    log_message "INFO" "  CPU负载: 40%-50% 随机"
    log_message "INFO" "  内存负载: 50%-60% 随机"
    log_message "INFO" "  运行时间: 5-6小时随机"
    log_message "INFO" "  停止时间: 1-3分钟随机"
}

# 加载配置
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        log_message "INFO" "已加载配置文件: $CONFIG_FILE"
    else
        log_message "INFO" "配置文件不存在，将创建默认配置"
        create_default_config
        source "$CONFIG_FILE"
    fi
}

# ==================== 实验管理函数 ====================

# 启动CPU负载实验（从守护程序移入）
start_cpu_experiment() {
    local cpu_percent=${1:-75}
    log_message "INFO" "启动CPU负载实验，负载率 ${cpu_percent}%"
    local result=$("$BLADE_BIN" create cpu fullload --cpu-percent "$cpu_percent")
    local status=$?
    if [[ $status -eq 0 ]]; then
        local uid=$(echo "$result" | grep -o '"result":"[^"]*"' | cut -d'"' -f4)
        if [[ -n "$uid" ]]; then
            log_message "INFO" "CPU负载实验创建成功，UID: $uid"
            echo "cpu|$uid|$(date '+%Y-%m-%d %H:%M:%S')" >> "$EXPERIMENT_FILE"
            return 0
        fi
    fi
    log_message "ERROR" "CPU负载实验创建失败: $result"
    return 1
}

# 启动内存负载实验（从守护程序移入）
start_memory_experiment() {
    local memory_percent=${1:-75}
    log_message "INFO" "启动内存负载实验，使用内存百分比 ${memory_percent}%"
    local result=$("$BLADE_BIN" create mem load --mode ram --mem-percent "$memory_percent" --rate 10240)
    local status=$?
    if [[ $status -eq 0 ]]; then
        local uid=$(echo "$result" | grep -o '"result":"[^"]*"' | cut -d'"' -f4)
        if [[ -n "$uid" ]]; then
            log_message "INFO" "内存负载实验创建成功，UID: $uid"
            echo "memory|$uid|$(date '+%Y-%m-%d %H:%M:%S')" >> "$EXPERIMENT_FILE"
            return 0
        fi
    fi
    log_message "ERROR" "内存负载实验创建失败: $result"
    return 1
}

# 查询实验状态
query_experiment_status() {
    local uid="$1"
    if [[ -n "$uid" ]]; then
        "$BLADE_BIN" status "$uid"
    else
        "$BLADE_BIN" status --type create
    fi
}

# 获取所有运行中实验的UID
get_running_experiment_uids() {
    local all_experiments=$("$BLADE_BIN" status --type create 2>/dev/null)
    if [[ -n "$all_experiments" ]]; then
        # 解析JSON数组，查找Status为Success的实验并提取Uid
        # result是数组格式，需要匹配数组中每个对象
        echo "$all_experiments" | grep -o '{"Uid":"[^"]*"[^}]*"Status":"Success"[^}]*}' | grep -o '"Uid":"[^"]*"' | cut -d'"' -f4
    fi
}

# 停止单个实验
stop_experiment() {
    local uid="$1"
    if [[ -n "$uid" ]]; then
        # 先查询实验状态
        local experiment_status=$(query_experiment_status "$uid" 2>/dev/null)
        # 单个查询返回的result是对象，直接解析Status字段
        local current_status=$(echo "$experiment_status" | grep -o '"Status":"[^"]*"' | cut -d'"' -f4)

        if [[ "$current_status" == "Destroyed" ]]; then
            log_message "INFO" "实验 $uid 已经是销毁状态，跳过"
            return 0
        elif [[ "$current_status" == "Success" ]]; then
            log_message "INFO" "停止运行中的实验: $uid"
            local result=$("$BLADE_BIN" destroy "$uid")
            local status=$?

            if [[ $status -eq 0 ]]; then
                log_message "INFO" "实验 $uid 已成功停止"
                return 0
            else
                log_message "ERROR" "停止实验 $uid 失败: $result"
                return 1
            fi
        else
            log_message "WARNING" "实验 $uid 状态未知: $current_status"
            return 1
        fi
    fi
}

# 停止所有实验
# 参数: $1 - 是否静默模式 (true/false，默认false)
stop_all_experiments() {
    local quiet_mode=${1:-false}
    local stopped_count=0
    local skipped_count=0

    if [[ "$quiet_mode" != "true" ]]; then
        log_message "INFO" "========== 开始停止所有 ChaosBlade 实验 =========="
    fi

    # 从实验文件中读取所有实验UID
    if [[ -f "$EXPERIMENT_FILE" ]]; then
        while IFS='|' read -r type uid time; do
            if [[ -n "$uid" ]]; then
                if [[ "$quiet_mode" == "true" ]]; then
                    # 静默模式：直接销毁，不检查状态
                    "$BLADE_BIN" destroy "$uid" &> /dev/null
                    ((stopped_count++))
                else
                    # 详细模式：检查状态并记录
                    if stop_experiment "$uid"; then
                        ((stopped_count++))
                    else
                        ((skipped_count++))
                    fi
                fi
            fi
        done < "$EXPERIMENT_FILE"

        # 清空实验文件
        > "$EXPERIMENT_FILE"
    fi

    # 查询并停止所有运行中的实验
    local running_uids=$(get_running_experiment_uids)
    if [[ -n "$running_uids" ]]; then
        while read -r uid; do
            if [[ -n "$uid" ]]; then
                if [[ "$quiet_mode" == "true" ]]; then
                    # 静默模式：直接销毁
                    "$BLADE_BIN" destroy "$uid" &> /dev/null
                    ((stopped_count++))
                else
                    # 详细模式：记录日志
                    log_message "INFO" "发现运行中的实验: $uid"
                    if stop_experiment "$uid"; then
                        ((stopped_count++))
                    else
                        ((skipped_count++))
                    fi
                fi
            fi
        done <<< "$running_uids"
    fi

    # 清理历史配置

    # 杀死守护进程（重构后新增）
    if [[ "$quiet_mode" != "true" ]]; then
        log_message "INFO" "正在查找并停止守护进程..."
    fi
    
    local daemon_killed=false
    
    # 优先使用PID文件终止守护进程
    if [[ -f "$PID_FILE" ]]; then
        local stored_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$stored_pid" ]] && kill -0 "$stored_pid" 2>/dev/null; then
            if [[ "$quiet_mode" != "true" ]]; then
                log_message "INFO" "停止守护进程: PID $stored_pid (从PID文件)"
            fi
            kill -TERM "$stored_pid" 2>/dev/null || true
            
            # 等待进程优雅退出
            local wait_count=0
            while kill -0 "$stored_pid" 2>/dev/null && [[ $wait_count -lt 5 ]]; do
                sleep 1
                ((wait_count++))
            done
            
            # 如果进程仍然存在，强制杀死
            if kill -0 "$stored_pid" 2>/dev/null; then
                if [[ "$quiet_mode" != "true" ]]; then
                    log_message "WARNING" "强制终止守护进程: PID $stored_pid"
                fi
                kill -KILL "$stored_pid" 2>/dev/null || true
            fi
            daemon_killed=true
        fi
        
        # 清理PID文件
        rm -f "$PID_FILE"
    fi
    
    # 如果PID文件方式没有成功，使用ps命令查找守护进程
    if [[ "$daemon_killed" == "false" ]]; then
        local daemon_pids=$(ps aux | grep "[c]haosblade_resource_manager.sh.*--daemon-mode" | awk '{print $2}')
        if [[ -n "$daemon_pids" ]]; then
            while read -r pid; do
                if [[ -n "$pid" ]]; then
                    if [[ "$quiet_mode" != "true" ]]; then
                        log_message "INFO" "停止守护进程: PID $pid (从进程列表)"
                    fi
                    kill -TERM "$pid" 2>/dev/null || true
                    
                    # 等待进程优雅退出
                    local wait_count=0
                    while kill -0 "$pid" 2>/dev/null && [[ $wait_count -lt 5 ]]; do
                        sleep 1
                        ((wait_count++))
                    done
                    
                    # 如果进程仍然存在，强制杀死
                    if kill -0 "$pid" 2>/dev/null; then
                        if [[ "$quiet_mode" != "true" ]]; then
                            log_message "WARNING" "强制终止守护进程: PID $pid"
                        fi
                        kill -KILL "$pid" 2>/dev/null || true
                    fi
                    daemon_killed=true
                fi
            done <<< "$daemon_pids"
        fi
    fi
    
    if [[ "$quiet_mode" != "true" ]]; then
        if [[ "$daemon_killed" == "true" ]]; then
            log_message "INFO" "所有守护进程已停止"
        else
            log_message "INFO" "未发现运行中的守护进程"
        fi
    fi

    # 清理实验记录文件
    if [[ -f "$EXPERIMENT_FILE" ]]; then
        > "$EXPERIMENT_FILE"
        if [[ "$quiet_mode" != "true" ]]; then
            log_message "INFO" "已清空实验记录文件: $EXPERIMENT_FILE"
        fi
    fi

    if [[ "$quiet_mode" != "true" ]]; then
        log_message "INFO" "========== ChaosBlade 实验停止完成 =========="
        log_message "INFO" "已停止实验数: $stopped_count"
        if [[ $skipped_count -gt 0 ]]; then
            log_message "INFO" "跳过实验数: $skipped_count (已销毁状态)"
        fi
        log_message "INFO" "已清理所有历史配置和临时文件"
    fi
}

# ==================== 循环管理函数 ====================

# 循环模式管理器（从守护程序移入）
cycle_manager() {
    log_message "INFO" "启动循环模式管理器"

    # 加载配置（如果不存在则创建默认配置）
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        log_message "INFO" "已加载配置文件: $CONFIG_FILE"
    else
        log_message "INFO" "配置文件不存在，创建默认配置: $CONFIG_FILE"
        create_default_config
        source "$CONFIG_FILE"
    fi

    local cycle_count=1
    local run_time_min=${CYCLE_RUN_TIME_MIN:-18000}
    local run_time_max=${CYCLE_RUN_TIME_MAX:-21600}
    local stop_time_min=${CYCLE_STOP_TIME_MIN:-60}
    local stop_time_max=${CYCLE_STOP_TIME_MAX:-180}

    log_message "INFO" "循环配置: 运行时间${run_time_min}-${run_time_max}秒, 停止时间${stop_time_min}-${stop_time_max}秒"

    while true; do
        local run_time=$(generate_random $run_time_min $run_time_max)
        local stop_time=$(generate_random $stop_time_min $stop_time_max)
        local cpu_percent_min=${CPU_PERCENT_MIN:-40}
        local cpu_percent_max=${CPU_PERCENT_MAX:-50}
        local memory_percent_min=${MEMORY_PERCENT_MIN:-50}
        local memory_percent_max=${MEMORY_PERCENT_MAX:-60}
        local cpu_percent=$(generate_random $cpu_percent_min $cpu_percent_max)
        local memory_percent=$(generate_random $memory_percent_min $memory_percent_max)

        log_message "INFO" "========== 开始第 $cycle_count 轮循环 =========="
        log_message "INFO" "本轮运行时间: ${run_time}秒 ($((run_time / 60))分钟)"
        log_message "INFO" "本轮停止时间: ${stop_time}秒 ($((stop_time / 60))分钟)"
        log_message "INFO" "本轮CPU负载: ${cpu_percent}%"
        log_message "INFO" "本轮内存负载: ${memory_percent}%"

        log_message "INFO" "启动负载实验..."
        start_cpu_experiment "$cpu_percent"
        start_memory_experiment "$memory_percent"

        local elapsed=0
        while [[ $elapsed -lt $run_time ]]; do
            sleep 30
            elapsed=$((elapsed + 30))
            if (( elapsed % 300 == 0 )); then
                local remaining=$((run_time - elapsed))
                log_message "INFO" "第${cycle_count}轮运行中，剩余时间: $((remaining / 60))分钟"
            fi
            if [[ ! -f "$PID_FILE" ]]; then
                log_message "INFO" "检测到停止信号，退出循环模式"
                return 0
            fi
        done

        log_message "INFO" "停止负载实验，准备休息 ${stop_time}秒 ($((stop_time / 60))分钟)"
        stop_all_experiments true

        local rest_elapsed=0
        while [[ $rest_elapsed -lt $stop_time ]]; do
            sleep 10
            rest_elapsed=$((rest_elapsed + 10))
            if [[ ! -f "$PID_FILE" ]]; then
                log_message "INFO" "检测到停止信号，退出循环模式"
                return 0
            fi
        done

        log_message "INFO" "========== 第 $cycle_count 轮循环完成 =========="
        cycle_count=$((cycle_count + 1))
    done
}

# ==================== 主程序功能函数 ====================


# 启动所有负载实验
start_load() {
    # 检查是否已有进程运行
    if [[ -f "$PID_FILE" ]]; then
        local existing_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$existing_pid" ]] && kill -0 "$existing_pid" 2>/dev/null; then
            log_message "ERROR" "资源管理器已在运行 (PID: $existing_pid)，请先停止后再启动"
            exit 1
        else
            log_message "INFO" "清理无效的PID文件"
            rm -f "$PID_FILE"
        fi
    fi

    log_message "INFO" "========== 开始启动 ChaosBlade 资源负载实验 =========="

    # 加载配置
    load_config

    # 清空实验文件
    > "$EXPERIMENT_FILE"

    # 启动循环模式
    log_message "INFO" "启动循环模式（随机时间和负载）"
    log_message "INFO" "运行时间: ${CYCLE_RUN_TIME_MIN:-18000}-${CYCLE_RUN_TIME_MAX:-21600}秒 (5-6小时随机)"
    log_message "INFO" "停止时间: ${CYCLE_STOP_TIME_MIN:-60}-${CYCLE_STOP_TIME_MAX:-180}秒 (1-3分钟随机)"
    log_message "INFO" "CPU负载: ${CPU_PERCENT_MIN:-40}-${CPU_PERCENT_MAX:-50}% 随机"
    log_message "INFO" "内存负载: ${MEMORY_PERCENT_MIN:-50}-${MEMORY_PERCENT_MAX:-60}% 随机"

    # 在后台启动守护模式（重构后的新方式）
    log_message "INFO" "启动循环管理器..."
    nohup "$0" --daemon-mode > /dev/null 2>&1 &
    local cycle_pid=$!
    echo "$cycle_pid" > "$PID_FILE"
    log_message "INFO" "已启动循环管理器 (PID: $cycle_pid)"
    
    # 等待守护进程启动完成并验证
    sleep 2
    if [[ -f "$PID_FILE" ]]; then
        local stored_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$stored_pid" ]] && kill -0 "$stored_pid" 2>/dev/null; then
            log_message "INFO" "守护进程启动成功，PID: $stored_pid"
        else
            log_message "ERROR" "守护进程启动失败，请检查日志"
            exit 1
        fi
    else
        log_message "ERROR" "PID文件不存在，守护进程启动失败"
        exit 1
    fi

    log_message "INFO" "========== ChaosBlade 资源负载实验启动完成 =========="
    log_message "INFO" "运行模式: 循环模式（随机时间和负载）"
    log_message "INFO" "  运行时间: 5-6小时随机"
    log_message "INFO" "  停止时间: 1-3分钟随机"
    log_message "INFO" "  CPU负载: 40%-50% 随机"
    log_message "INFO" "  内存负载: 50%-60% 随机"
    log_message "INFO" "实验文件: $EXPERIMENT_FILE"
    log_message "INFO" "日志文件: $LOG_FILE"
}

# ==================== 状态管理函数 ====================

# 显示系统状态
show_status() {
    log_message "INFO" "========== 系统资源状态 =========="

    # 当前状态
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local memory_usage=$(free | grep Mem | awk '{printf("%.2f"), $3/$2 * 100.0}')
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')

    log_message "INFO" "当前CPU使用率: ${cpu_usage}%"
    log_message "INFO" "当前内存使用率: ${memory_usage}%"
    log_message "INFO" "系统负载: ${load_avg}"

    # 负载配置信息
    if [[ -f "$CONFIG_FILE" ]]; then
        source "$CONFIG_FILE"
        log_message "INFO" "运行模式: 循环模式（随机时间和负载）"
        log_message "INFO" "  运行时间: ${CYCLE_RUN_TIME_MIN:-18000}-${CYCLE_RUN_TIME_MAX:-21600}秒 (5-6小时)"
        log_message "INFO" "  停止时间: ${CYCLE_STOP_TIME_MIN:-60}-${CYCLE_STOP_TIME_MAX:-180}秒 (1-3分钟)"
        log_message "INFO" "  CPU负载: ${CPU_PERCENT_MIN:-40}-${CPU_PERCENT_MAX:-50}% 随机"
        log_message "INFO" "  内存负载: ${MEMORY_PERCENT_MIN:-50}-${MEMORY_PERCENT_MAX:-60}% 随机"
    fi

    # ChaosBlade 实验状态
    log_message "INFO" "ChaosBlade 实验状态:"
    if [[ -f "$EXPERIMENT_FILE" && -s "$EXPERIMENT_FILE" ]]; then
        local experiment_count=$(wc -l < "$EXPERIMENT_FILE")
        log_message "INFO" "活跃实验数: $experiment_count"

        # 显示每个实验的状态
        while IFS='|' read -r type uid time; do
            if [[ -n "$uid" ]]; then
                # 单个查询返回的result是对象，直接解析Status字段
                local status=$(query_experiment_status "$uid" 2>/dev/null | grep -o '"Status":"[^"]*"' | cut -d'"' -f4)
                local status_display=""
                case "$status" in
                    "Success") status_display="运行中" ;;
                    "Destroyed") status_display="已销毁" ;;
                    *) status_display="${status:-未知}" ;;
                esac
                log_message "INFO" "  实验类型: $type, UID: $uid, 状态: $status_display"
            fi
        done < "$EXPERIMENT_FILE"
    else
        log_message "INFO" "活跃实验数: 0"
    fi

    # 运行状态
    if [[ -f "$PID_FILE" ]]; then
        local stored_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [[ -n "$stored_pid" ]] && kill -0 "$stored_pid" 2>/dev/null; then
            log_message "INFO" "运行状态: 运行中 (PID: $stored_pid)"
        else
            log_message "INFO" "运行状态: 已停止 (PID文件无效)"
        fi
    else
        log_message "INFO" "运行状态: 已停止"
    fi

    log_message "INFO" "=========================================="
}

# 配置管理
configure() {
    log_message "INFO" "========== 配置管理 =========="

    echo "选择操作："
    echo "1. 创建默认配置"
    echo "2. 查看当前配置"
    echo "请选择 (1-2): "
    read -r choice

    case $choice in
        1)
            log_message "INFO" "创建默认配置..."
            create_default_config
            log_message "INFO" "默认配置已创建："
            log_message "INFO" "  运行时间: 5-6小时随机"
            log_message "INFO" "  停止时间: 1-3分钟随机"
            log_message "INFO" "  CPU负载: 40%-50% 随机"
            log_message "INFO" "  内存负载: 50%-60% 随机"
            ;;
        2)
            if [[ -f "$CONFIG_FILE" ]]; then
                echo "当前配置："
                cat "$CONFIG_FILE"
            else
                echo "配置文件不存在，将创建默认配置"
                create_default_config
            fi
            ;;
        *)
            log_message "ERROR" "无效选择"
            ;;
    esac
}

# 监控模式
monitor() {
    log_message "INFO" "进入监控模式 (按Ctrl+C退出)"

    while true; do
        clear
        echo "==============================================="
        echo "        ChaosBlade 资源管理器监控面板"
        echo "==============================================="
        echo "时间: $(date)"
        echo ""

        # 显示系统信息
        show_status

        echo ""
        echo "==============================================="
        echo "按 Ctrl+C 退出监控模式"
        sleep 5
    done
}

# 彻底清理所有相关文件和配置
clean_all_configs() {
    log_message "INFO" "========== 开始彻底清理所有配置 =========="

    # 停止所有实验
    stop_all_experiments true

    # 清理所有配置文件
    if [[ -f "$CONFIG_FILE" ]]; then
        rm -f "$CONFIG_FILE"
        log_message "INFO" "已删除配置文件: $CONFIG_FILE"
    fi

    if [[ -f "$EXPERIMENT_FILE" ]]; then
        rm -f "$EXPERIMENT_FILE"
        log_message "INFO" "已删除实验文件: $EXPERIMENT_FILE"
    fi

    if [[ -f "$PID_FILE" ]]; then
        rm -f "$PID_FILE"
        log_message "INFO" "已删除PID文件: $PID_FILE"
    fi

    # 清理守护进程脚本（守护程序重构后不再需要）
    # 注意：重构后不再创建守护进程脚本文件，因此不需要清理

    # 清理日志文件（可选）
    if [[ -f "$LOG_FILE" ]]; then
        echo "是否清理日志文件? (y/N): "
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            rm -f "$LOG_FILE"
            log_message "INFO" "已删除日志文件: $LOG_FILE"
        fi
    fi

    # 如果工作目录为空，删除整个目录
    if [[ -d "$SCRIPT_DIR" ]] && [[ -z "$(ls -A "$SCRIPT_DIR" 2>/dev/null)" ]]; then
        rmdir "$SCRIPT_DIR"
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] 已删除空的工作目录: $SCRIPT_DIR"
    fi

    log_message "INFO" "========== 彻底清理完成 =========="
}

# 清理函数
cleanup() {
    log_message "INFO" "正在清理资源..."
    stop_all_experiments
    
    # 如果是守护进程，清理PID文件
    if [[ "$1" == "--daemon-mode" ]]; then
        rm -f "$PID_FILE"
        log_message "INFO" "守护进程退出，已清理PID文件"
    fi
    
    exit 0
}

# 信号处理
trap cleanup SIGINT SIGTERM

# 显示帮助信息
show_help() {
    cat << EOF
ChaosBlade 资源利用率管理脚本

用法: $0 {start|stop|status|config|monitor|clean|help}

命令说明:
    start    - 启动自动资源负载实验
    stop     - 停止所有实验并清理历史配置
    status   - 显示系统资源和实验状态
    config   - 配置管理
    monitor  - 进入监控模式
    clean    - 彻底清理所有配置文件和历史记录
    help     - 显示帮助信息

特性:
    - 简化的随机负载分配（CPU 40%-50%随机，内存50%-60%随机）
    - 基于 ChaosBlade 的专业混沌工程能力
    - 支持循环模式（运行-停止-运行循环，时间随机）
    - 完全自动化，无需复杂配置
    - 安全的实验管理

文件说明:
    配置文件: $CONFIG_FILE
    实验文件: $EXPERIMENT_FILE
    日志文件: $LOG_FILE
    PID文件: $PID_FILE
    工作目录: $SCRIPT_DIR

示例:
    $0 start    # 启动自动负载实验
    $0 status   # 查看资源状态
    $0 config   # 配置管理
    $0 monitor  # 实时监控
    $0 clean    # 彻底清理所有配置
EOF
}

# 主函数
main() {
    # 守护模式检测（重构新增）
    if [[ "$1" == "--daemon-mode" ]]; then
        log_message "INFO" "进入守护模式"
        # 重新创建 PID 文件，确保记录正确的进程 ID
        echo "$$" > "$PID_FILE"
        log_message "INFO" "守护进程 PID: $$"
        # 加载配置并启动循环管理器
        load_config
        # 设置信号处理 - 守护进程退出时清理PID文件
        trap 'cleanup --daemon-mode' SIGTERM SIGINT SIGQUIT
        # 启动循环管理器
        cycle_manager
        # 正常退出时也清理PID文件
        rm -f "$PID_FILE"
        exit $?
    fi

    check_root

    case "${1:-help}" in
        start)
            # 检查 ChaosBlade 是否已安装
            if [[ ! -f "$BLADE_BIN" ]]; then
                log_message "ERROR" "ChaosBlade 未安装，请使用离线安装脚本安装"
                log_message "INFO" "请参考 README_DEPLOYMENT.md 中的安装说明"
                exit 1
            fi
            start_load
            ;;
        stop)
            if [[ ! -f "$BLADE_BIN" ]]; then
                log_message "ERROR" "ChaosBlade 未安装，请使用离线安装脚本安装"
                exit 1
            fi
            stop_all_experiments
            ;;
        status)
            if [[ ! -f "$BLADE_BIN" ]]; then
                log_message "ERROR" "ChaosBlade 未安装，请使用离线安装脚本安装"
                exit 1
            fi
            show_status
            ;;
        config)
            configure
            ;;
        monitor)
            if [[ ! -f "$BLADE_BIN" ]]; then
                log_message "ERROR" "ChaosBlade 未安装，请使用离线安装脚本安装"
                exit 1
            fi
            monitor
            ;;
        clean)
            clean_all_configs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_message "ERROR" "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
