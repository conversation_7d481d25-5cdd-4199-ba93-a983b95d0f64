# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 ChaosBlade 混沌工程工具的资源管理项目，包含两个主要的资源负载管理脚本：

1. **ChaosBlade 资源管理器** (v2.0 重构版) - 基于阿里 ChaosBlade 的单机资源负载测试
2. **动态队列管理器** - 基于 Spark SQL 的多队列并发资源管理

## 核心开发命令

### 脚本测试和调试
```bash
# ChaosBlade 资源管理器测试
./chaosblade_resource_manager.sh help
./chaosblade_resource_manager.sh status
./chaosblade_resource_manager.sh config

# 动态队列管理器测试
./dynamic_queue_manager.sh help
./dynamic_queue_manager.sh status
./dynamic_queue_manager.sh config

# 语法检查
bash -n chaosblade_resource_manager.sh
bash -n dynamic_queue_manager.sh
```

### 部署测试
```bash
# 离线包生成测试
./download_chaosblade_offline.sh

# Ansible 部署测试
ansible-playbook -i ansible/inventory/hosts.ini ansible/chaosblade-deploy.yml --check
ansible-playbook -i ansible/inventory/hosts.ini ansible/chaosblade-deploy.yml --limit "target_host"
```

### 代码质量检查
```bash
# 检查脚本权限
chmod +x *.sh

# 检查文件结构
find . -name "*.sh" -exec head -5 {} \; -exec echo "---" \;

# 检查关键配置
grep -n "SCRIPT_NAME\|LOG_FILE\|PID_FILE" *.sh
```

## 🚀 v2.0 重构版本重要变更

### ChaosBlade 资源管理器重构

- **代码量减少 20%**: 797 行 → 644 行
- **消除重复代码**: 移除了 5 个重复函数定义
- **简化架构**: 统一主程序和守护程序逻辑，移除 474 行的 EOF 脚本生成
- **保持功能完整**: 所有原有功能保持不变

### 新架构设计

```bash
# 旧架构（复杂）
start_load() → create_daemon_script() → 生成474行EOF脚本 → nohup执行临时脚本

# 新架构（简洁）
start_load() → 直接启动守护模式 → cycle_manager()
```

## 核心脚本

### 1. chaosblade_resource_manager.sh (v2.0 重构版)

- **功能**: 基于 ChaosBlade 的智能资源负载管理
- **特性**: 支持循环模式，随机时间和负载（70%-90%随机）
- **依赖**: ChaosBlade 1.7.4，需要通过离线安装包部署
- **新特性**:
  - 统一脚本架构，通过`--daemon-mode`参数控制运行模式
  - 消除所有重复代码，代码结构更清晰
  - 不再生成临时守护程序脚本文件
- **主要命令**:
  ```bash
  ./chaosblade_resource_manager.sh start    # 启动循环负载测试
  ./chaosblade_resource_manager.sh stop     # 停止所有实验
  ./chaosblade_resource_manager.sh status   # 查看系统状态
  ./chaosblade_resource_manager.sh monitor  # 实时监控
  ./chaosblade_resource_manager.sh config   # 配置管理
  ```

### 2. dynamic_queue_manager.sh

- **功能**: Spark SQL 多队列并发资源管理（641 个节点集群优化）
- **特性**: 支持 10 个不同队列的并发资源占用，精确的 executor 配置算法
- **依赖**: FusionInsight Spark2x, YARN, Kerberos 认证
- **主要命令**:
  ```bash
  ./dynamic_queue_manager.sh start     # 启动多队列管理器
  ./dynamic_queue_manager.sh stop      # 停止所有队列任务
  ./dynamic_queue_manager.sh status    # 查看运行状态
  ./dynamic_queue_manager.sh config    # 查看队列配置
  ./dynamic_queue_manager.sh cleanup   # 清理YARN应用
  ```

## v2.0 代码结构重组

### 函数组织架构

```bash
# ==================== 基础工具函数 ====================
log_message()              # 统一日志函数
check_root()               # 权限检查
generate_random()          # 随机数生成

# ==================== 配置管理函数 ====================
create_default_config()    # 配置创建
load_config()              # 配置加载

# ==================== 实验管理函数 ====================
start_cpu_experiment()     # CPU实验启动
start_memory_experiment()  # 内存实验启动
query_experiment_status()  # 统一实验查询
get_running_experiment_uids()  # 统一UID获取
stop_experiment()          # 统一实验停止
stop_all_experiments()     # 统一批量停止

# ==================== 循环管理函数 ====================
cycle_manager()            # 主循环逻辑（从守护程序移入）

# ==================== 主程序功能函数 ====================
start_load()               # 启动负载（重构：直接启动守护模式）
show_status()              # 状态显示
configure()                # 配置管理
monitor()                  # 监控模式
```

## 部署和安装

### ChaosBlade 离线部署架构

项目采用分离式部署架构，避免循环依赖：

**有网环境准备**:
```bash
./download_chaosblade_offline.sh  # 生成离线包
```

**内网环境部署**:
- 上传 `chaosblade_offline_package_v1.7.4.tar.gz` (离线包)
- 上传 `ansible/` 目录 (批量部署配置)

### 单机部署

```bash
tar -xzf chaosblade_offline_package_v1.7.4.tar.gz
cd chaosblade_offline_package
sudo ./install_offline.sh  # 自动创建 systemd 服务
sudo systemctl start chaosblade-resource-manager
```

### 多节点批量部署 (Ansible)

```bash
# 配置主机清单
vim ansible/inventory/hosts.ini

# 执行批量部署
ansible-playbook -i ansible/inventory/hosts.ini ansible/chaosblade-deploy.yml

# 启动服务
ansible chaosblade_nodes -i ansible/inventory/hosts.ini -m shell -a "systemctl start chaosblade-resource-manager"
```

### Spark 队列管理器部署

- 确保 FusionInsight 环境配置正确
- 配置 Kerberos 认证 (`/data/user.keytab`)
- 确认 Spark SQL 和 YARN 二进制文件路径

## 配置文件位置

### ChaosBlade 相关

- 工作目录: `/opt/chaosblade-resource-manager/`
- 配置文件: `/opt/chaosblade-resource-manager/config.conf`
- 日志文件: `/opt/chaosblade-resource-manager/resource_manager.log`
- 实验记录: `/opt/chaosblade-resource-manager/experiments.txt`

### Spark 队列管理器相关

- 日志目录: `./logs/`
- PID 文件: `./dynamic_queue_manager.pid`
- 应用数据: `./app_data/`
- Spark 日志: `./spark_*.log`

## 队列资源配置

支持的队列及其资源配置：

- `hive_itd_model`: 12288GB 内存, 3072vcores
- `hive_itd_yx_vb8`: 20480GB 内存, 5120vcores
- `hive_itd_bonc_zydc`: 20480GB 内存, 2500vcores
- `yn_lakehouse`: 7000GB 内存, 3500vcores
- 以及其他 6 个队列（详见脚本配置）

## 安全机制

### ChaosBlade 保护机制

- CPU 保护: 使用率超过 90%自动停止实验
- 内存保护: 使用率超过 85%自动停止实验
- 自动超时设置
- 安全的实验管理和清理

### Spark 队列保护

- 智能资源分配算法，考虑内存和 CPU 双重约束
- 最大 executor 限制（641 节点 ×4=2564 个）
- 优雅的进程终止和资源清理
- 应用状态监控和自动恢复

## 运行模式

### ChaosBlade 循环模式

- 运行时间: 50-60 分钟随机
- 停止时间: 1-3 分钟随机
- CPU 负载: 70%-90%随机
- 内存负载: 70%-90%随机

### Spark 多队列模式

- 所有队列同时并发运行
- 独立的生命周期管理
- 资源级别: 70%-90%随机
- 智能 executor 配置优化

## 故障排除

### 常见命令

```bash
# ChaosBlade 手动清理
blade status --type create
blade destroy [实验UID]

# Spark 应用清理
yarn application -list -appStates RUNNING
yarn application -kill [Application ID]

# 日志查看
tail -f /opt/chaosblade-resource-manager/resource_manager.log
tail -f ./logs/dynamic_queue_manager.log
```

### 权限要求

- ChaosBlade: 建议 sudo 权限运行
- Spark: 需要有效的 Kerberos 认证
- 文件系统: 需要写权限到工作目录

## 重要架构特性

### PID 文件管理机制

ChaosBlade 资源管理器使用 PID 文件 (`/opt/chaosblade-resource-manager/daemon.pid`) 进行进程管理：
- 守护进程启动时创建 PID 文件
- 停止时清理 PID 文件
- 支持通过 PID 精确验证进程状态

### 分离式部署架构

避免循环依赖的设计：
- **离线包**: 包含 ChaosBlade 工具、脚本和安装程序
- **Ansible 配置**: 独立的批量部署配置，操作离线包
- **systemd 服务**: 由 `install_offline.sh` 自动创建，支持标准服务管理

### 安全清理机制

- 部署前自动清理已存在的安装目录
- 安全检查防止误删根目录 (`when: directory != "/"`)
- 优雅停止和强制终止的两阶段进程管理

## 开发架构要点

### 脚本架构模式
- **统一入口**: 所有脚本采用相同的命令行参数模式 (`start|stop|status|config|monitor|help`)
- **守护进程模式**: 通过 `--daemon-mode` 参数控制运行模式，避免临时脚本文件生成
- **PID 文件管理**: 使用 PID 文件进行进程状态跟踪和管理
- **配置文件分离**: 配置与代码分离，支持动态配置加载

### 错误处理机制
- **资源保护**: CPU 90%、内存 85% 自动停止保护
- **超时机制**: 自动超时设置防止僵尸进程
- **优雅退出**: 支持 SIGTERM 和 SIGINT 信号处理
- **清理机制**: 退出时自动清理临时文件和进程

### 日志和监控
- **统一日志格式**: `[timestamp] [level] message`
- **日志轮转**: 自动管理日志文件大小
- **实时监控**: 支持 `monitor` 命令实时查看系统状态
- **实验记录**: 记录所有实验 UID 用于清理和恢复

## 开发工作流程

### 脚本修改和测试流程
```bash
# 1. 修改脚本后的语法验证
bash -n chaosblade_resource_manager.sh && echo "ChaosBlade脚本语法正确"
bash -n dynamic_queue_manager.sh && echo "动态队列脚本语法正确"

# 2. 功能测试（不实际启动负载）
./chaosblade_resource_manager.sh config   # 检查配置生成
./chaosblade_resource_manager.sh status   # 检查状态查询
./dynamic_queue_manager.sh config         # 检查队列配置

# 3. 依赖检查
which blade >/dev/null 2>&1 && echo "ChaosBlade可用" || echo "ChaosBlade未安装"
which spark-sql >/dev/null 2>&1 && echo "Spark SQL可用" || echo "需要配置Spark环境"
```

### 部署包生成和验证
```bash
# 1. 生成离线包（需要网络环境）
./download_chaosblade_offline.sh

# 2. 验证离线包完整性
tar -tzf chaosblade_offline_package_v1.7.4.tar.gz | head -10

# 3. Ansible 配置验证
ansible-inventory -i ansible/inventory/hosts.ini --list
ansible chaosblade_nodes -i ansible/inventory/hosts.ini -m ping
```

## 测试和验证

### 快速验证脚本状态
```bash
# 快速检查所有脚本是否可执行
ls -la *.sh

# 验证脚本语法
bash -n chaosblade_resource_manager.sh && echo "ChaosBlade脚本语法正确"
bash -n dynamic_queue_manager.sh && echo "动态队列脚本语法正确"

# 检查依赖和环境
./chaosblade_resource_manager.sh help
./dynamic_queue_manager.sh help
```

### 调试和日志分析
```bash
# 实时查看日志
tail -f /opt/chaosblade-resource-manager/resource_manager.log
tail -f ./logs/dynamic_queue_manager.log

# 检查进程状态
ps aux | grep -E "(chaosblade|spark-sql)" | grep -v grep

# 检查资源使用
top -p $(pgrep -f "chaosblade_resource_manager\|dynamic_queue_manager")
```

## 注意事项

1. **v2.0 重构优势**: 代码更简洁，维护更容易，调试更方便
2. **资源监控**: 两个脚本都有完善的资源保护机制，但仍需监控系统状态
3. **依赖检查**: 启动前会自动检查必要的依赖文件和程序
4. **清理机制**: 支持优雅停止和强制清理，确保不留残留进程
5. **日志轮转**: 会自动清理旧的日志文件，保持磁盘空间
