#!/bin/bash
# 文件名: download_chaosblade_offline.sh
# 功能: 下载 ChaosBlade 离线安装包
# 用法: ./download_chaosblade_offline.sh [版本号]

set -e

# 配置
CHAOSBLADE_VERSION="${1:-1.7.4}"
DOWNLOAD_DIR="chaosblade_offline_package"
CHAOSBLADE_URL="https://github.com/chaosblade-io/chaosblade/releases/download/v${CHAOSBLADE_VERSION}/chaosblade-${CHAOSBLADE_VERSION}-linux-amd64.tar.gz"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查网络连接
check_network() {
    log_info "检查网络连接..."
    if ! ping -c 1 github.com &> /dev/null; then
        log_error "无法连接到 GitHub，请检查网络连接"
        exit 1
    fi
    log_info "网络连接正常"
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    local missing_tools=()
    
    if ! command -v wget &> /dev/null && ! command -v curl &> /dev/null; then
        missing_tools+=("wget 或 curl")
    fi
    
    if ! command -v tar &> /dev/null; then
        missing_tools+=("tar")
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        log_info "请安装后重试"
        exit 1
    fi
    
    log_info "依赖工具检查完成"
}

# 创建下载目录
create_download_dir() {
    log_info "创建下载目录: $DOWNLOAD_DIR"

    if [[ -d "$DOWNLOAD_DIR" ]]; then
        log_warn "目录已存在，将清空重新创建"
        rm -rf "$DOWNLOAD_DIR"
    fi

    mkdir -p "$DOWNLOAD_DIR"
    cd "$DOWNLOAD_DIR"
}



# 下载 ChaosBlade
download_chaosblade() {
    local filename="chaosblade-${CHAOSBLADE_VERSION}-linux-amd64.tar.gz"

    # 检查根目录是否有备份文件
    if [[ -f "../$filename" ]]; then
        log_info "发现根目录备份文件，验证完整性..."

        # 验证根目录的备份文件
        if tar -tzf "../$filename" &> /dev/null; then
            log_info "根目录备份文件验证成功，复制到下载目录"
            cp "../$filename" "$filename"
            return 0
        else
            log_warn "根目录备份文件损坏，删除并重新下载"
            rm -f "../$filename"
        fi
    fi

    log_info "下载 ChaosBlade v${CHAOSBLADE_VERSION}..."
    log_info "下载地址: $CHAOSBLADE_URL"

    # 执行下载
    if command -v wget &> /dev/null; then
        if ! wget -O "$filename" "$CHAOSBLADE_URL"; then
            log_error "wget 下载失败"
            exit 1
        fi
    elif command -v curl &> /dev/null; then
        if ! curl -L -o "$filename" "$CHAOSBLADE_URL"; then
            log_error "curl 下载失败"
            exit 1
        fi
    else
        log_error "未找到 wget 或 curl 工具"
        exit 1
    fi

    # 验证下载
    if [[ ! -f "$filename" || ! -s "$filename" ]]; then
        log_error "下载失败或文件为空"
        exit 1
    fi

    log_info "ChaosBlade 下载完成: $filename"

    # 验证压缩包
    if tar -tzf "$filename" &> /dev/null; then
        log_info "压缩包验证成功"

        # 备份到根目录
        log_info "备份文件到根目录..."
        cp "$filename" "../$filename"
        log_info "文件已备份到根目录: ../$filename"
    else
        log_error "压缩包验证失败，可能文件损坏"
        rm -f "$filename"  # 删除损坏的文件
        exit 1
    fi
}



# 复制脚本文件
copy_scripts() {
    log_info "复制脚本文件..."

    # 复制主脚本
    if [[ -f "../chaosblade_resource_manager.sh" ]]; then
        cp "../chaosblade_resource_manager.sh" .
        log_info "已复制主脚本: chaosblade_resource_manager.sh"
    else
        log_warn "未找到主脚本文件，请手动复制 chaosblade_resource_manager.sh"
    fi

}

# 创建离线安装脚本
create_offline_installer() {
    log_info "创建离线安装脚本..."

    cat > "install_offline.sh" << 'EOF'
#!/bin/bash
# ChaosBlade 离线安装脚本

set -e

# 配置
CHAOSBLADE_DIR="/opt/chaosblade"

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1"
}

# 检查权限
if [[ $EUID -ne 0 ]]; then
    log_error "请以 root 权限运行此脚本"
    exit 1
fi

# 安装 ChaosBlade
install_chaosblade() {
    log_info "开始离线安装 ChaosBlade..."
    
    # 查找 ChaosBlade 压缩包
    local tarfile=$(find . -name "chaosblade-*.tar.gz" | head -1)
    if [[ -z "$tarfile" ]]; then
        log_error "未找到 ChaosBlade 压缩包"
        exit 1
    fi
    
    log_info "找到压缩包: $tarfile"
    
    # 清理并创建安装目录
    if [[ -d "$CHAOSBLADE_DIR" ]]; then
        log_info "清理已存在的 ChaosBlade 安装目录: $CHAOSBLADE_DIR"
        rm -rf "$CHAOSBLADE_DIR"
    fi
    mkdir -p "$CHAOSBLADE_DIR"
    
    # 解压到临时目录
    local temp_dir=$(mktemp -d)
    tar -xzf "$tarfile" -C "$temp_dir"
    
    # 移动文件到安装目录
    local extracted_dir=$(find "$temp_dir" -name "chaosblade-*" -type d | head -1)
    if [[ -n "$extracted_dir" ]]; then
        cp -r "$extracted_dir"/* "$CHAOSBLADE_DIR/"
    else
        log_error "解压失败或目录结构异常"
        exit 1
    fi
    
    # 设置权限
    chmod +x "$CHAOSBLADE_DIR/blade"
    
    # 创建软链接
    if [[ ! -f "/usr/local/bin/blade" ]]; then
        ln -s "$CHAOSBLADE_DIR/blade" /usr/local/bin/blade
    fi
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    # 验证安装
    if "$CHAOSBLADE_DIR/blade" version &> /dev/null; then
        local version_output=$("$CHAOSBLADE_DIR/blade" version 2>/dev/null)
        local version=$(echo "$version_output" | grep -oE "v[0-9]+\.[0-9]+\.[0-9]+" | head -1)
        if [[ -z "$version" ]]; then
            # 备用版本提取方法
            version=$(echo "$version_output" | grep -oE "[0-9]+\.[0-9]+\.[0-9]+" | head -1)
            if [[ -n "$version" ]]; then
                version="v$version"
            else
                version="未知版本"
            fi
        fi
        log_info "ChaosBlade 安装成功，版本: $version"
    else
        log_error "ChaosBlade 安装失败"
        exit 1
    fi
}

# 安装脚本
install_script() {
    log_info "安装资源管理脚本..."
    
    if [[ -f "chaosblade_resource_manager.sh" ]]; then
        # 备份现有脚本（如果存在）
        if [[ -f "/usr/local/bin/chaosblade_resource_manager.sh" ]]; then
            log_info "备份现有脚本..."
            cp "/usr/local/bin/chaosblade_resource_manager.sh" "/usr/local/bin/chaosblade_resource_manager.sh.backup.$(date +%Y%m%d_%H%M%S)"
        fi
        
        cp "chaosblade_resource_manager.sh" /usr/local/bin/
        chmod +x /usr/local/bin/chaosblade_resource_manager.sh
        log_info "脚本已安装到 /usr/local/bin/chaosblade_resource_manager.sh"
    else
        log_error "未找到资源管理脚本"
        exit 1
    fi
}

# 创建系统服务
create_systemd_service() {
    log_info "创建系统服务..."
    
    # 调试信息：检查现有服务状态
    log_info "检查现有服务状态..."
    systemctl status chaosblade-resource-manager --no-pager --lines=0 2>/dev/null || log_info "服务不存在或已停止"
    
    # 停止并禁用旧服务（如果存在）
    if systemctl is-active --quiet chaosblade-resource-manager 2>/dev/null; then
        log_info "停止现有服务..."
        systemctl stop chaosblade-resource-manager 2>/dev/null || true
    fi
    
    if systemctl is-enabled --quiet chaosblade-resource-manager 2>/dev/null; then
        log_info "禁用现有服务..."
        systemctl disable chaosblade-resource-manager 2>/dev/null || true
    fi
    
    # 删除旧的服务文件和符号链接
    if [[ -f /etc/systemd/system/chaosblade-resource-manager.service ]]; then
        log_info "删除旧的服务文件..."
        rm -f /etc/systemd/system/chaosblade-resource-manager.service
    fi
    
    # 删除可能存在的符号链接
    if [[ -L /etc/systemd/system/multi-user.target.wants/chaosblade-resource-manager.service ]]; then
        log_info "删除旧的符号链接..."
        rm -f /etc/systemd/system/multi-user.target.wants/chaosblade-resource-manager.service
    fi
    
    # 创建新的服务文件
    cat > /etc/systemd/system/chaosblade-resource-manager.service << 'SERVICE_EOF'
[Unit]
Description=ChaosBlade Resource Manager
After=network.target
Wants=network.target

[Service]
Type=simple
ExecStart=/usr/local/bin/chaosblade_resource_manager.sh --daemon-mode
ExecStop=/usr/local/bin/chaosblade_resource_manager.sh stop
User=root
Group=root
Restart=on-failure
RestartSec=10
RestartPreventExitStatus=0 1 2 3 15
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# 运行时目录
RuntimeDirectory=chaosblade-resource-manager
RuntimeDirectoryMode=0755

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 安全设置（为 ChaosBlade 混沌工程工具放宽限制）
ProtectSystem=false
ProtectHome=false
NoNewPrivileges=false

[Install]
WantedBy=multi-user.target
SERVICE_EOF
    
    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用服务（开机自启）
    if systemctl enable chaosblade-resource-manager.service 2>/dev/null; then
        log_info "服务已启用"
    else
        # 如果启用失败，尝试强制启用
        log_info "常规启用失败，尝试强制启用..."
        if systemctl enable --force chaosblade-resource-manager.service 2>/dev/null; then
            log_info "服务已强制启用"
        else
            log_error "启用服务失败，请手动执行: systemctl enable chaosblade-resource-manager"
            # 不退出，继续执行，因为服务文件已创建
        fi
    fi
    
    # 验证服务文件是否创建成功
    if [[ -f /etc/systemd/system/chaosblade-resource-manager.service ]]; then
        log_info "系统服务已创建"
        
        # 检查服务状态
        if systemctl is-enabled --quiet chaosblade-resource-manager 2>/dev/null; then
            log_info "服务已启用"
        else
            log_info "服务文件已创建，但可能需要手动启用"
        fi
        
        log_info "使用方法:"
        log_info "  启动服务: systemctl start chaosblade-resource-manager"
        log_info "  停止服务: systemctl stop chaosblade-resource-manager"
        log_info "  查看状态: systemctl status chaosblade-resource-manager"
        log_info "  查看日志: journalctl -u chaosblade-resource-manager -f"
        log_info "  开机自启: systemctl enable chaosblade-resource-manager"
        log_info ""
        log_info "注意: 服务采用 Type=simple 模式，直接运行守护进程"
    else
        log_error "服务文件创建失败"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始 ChaosBlade 离线安装..."
    
    # 检查是否重复安装
    if [[ -f /usr/local/bin/chaosblade_resource_manager.sh ]] || [[ -d /opt/chaosblade ]]; then
        log_info "检测到现有安装，将进行升级安装..."
    fi
    
    install_chaosblade
    install_script
    create_systemd_service
    
    log_info "========================================="
    log_info "安装完成！"
    log_info "推荐使用 systemctl 管理服务:"
    log_info "  systemctl start chaosblade-resource-manager"
    log_info "  systemctl status chaosblade-resource-manager"
    log_info "或直接使用脚本: chaosblade_resource_manager.sh help"
    log_info "========================================="
}

main "$@"
EOF

    chmod +x "install_offline.sh"
    log_info "离线安装脚本已创建: install_offline.sh"
}


# 创建压缩包
create_package() {
    cd ..
    log_info "创建部署压缩包..."

    local package_name="chaosblade_offline_package_v${CHAOSBLADE_VERSION}.tar.gz"
    tar -czf "$package_name" "$DOWNLOAD_DIR"

    log_info "部署包已创建: $package_name"
    log_info "文件大小: $(du -h "$package_name" | cut -f1)"
    
    # 清理下载目录
    log_info "清理临时下载目录..."
    rm -rf "$DOWNLOAD_DIR"
    log_info "临时目录已清理完成"
}

# 主函数
main() {
    log_info "开始下载 ChaosBlade 离线部署包..."
    log_info "版本: v${CHAOSBLADE_VERSION}"

    check_network
    check_dependencies
    create_download_dir
    download_chaosblade
    copy_scripts
    create_offline_installer
    create_package

    log_info "=========================================="
    log_info "离线部署包准备完成！"
    log_info "部署包位置: chaosblade_offline_package_v${CHAOSBLADE_VERSION}.tar.gz"
    log_info "请将此文件上传到目标服务器并参考 DEPLOYMENT_GUIDE.md 进行部署"
    log_info "=========================================="
}

# 执行主函数
main "$@"
