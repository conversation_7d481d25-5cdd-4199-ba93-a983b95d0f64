#!/bin/bash

# 加载环境变量,以确保Spark等配置生效
if [[ -f ~/.bashrc ]]; then
    source ~/.bashrc
fi

# 动态队列资源占用脚本
# 用途: 在70-90%之间动态调整Spark SQL队列资源使用率
# 作者: Weizm
# 版本: 2.0

# ==================== 配置区域 ====================

# 基础配置
SCRIPT_NAME="dynamic_queue_manager"
LOG_DIR="$(dirname "$0")/logs"
PID_FILE="$(dirname "$0")/${SCRIPT_NAME}.pid"
LOG_FILE="${LOG_DIR}/${SCRIPT_NAME}.log"

# Spark基础配置
SPARK_SQL_BIN="/data/FusionInsight/Spark2x/spark/bin/spark-sql"
YARN_BIN="/data/FusionInsight/HDFS/hadoop/bin/yarn"
MASTER="yarn"
DEPLOY_MODE="client"
DRIVER_MEMORY="40g"
PRINCIPAL="<EMAIL>"
KEYTAB="/data/user.keytab"

# 多队列配置 - 队列名:内存大小(GB):CPU_vcores
declare -A QUEUE_CONFIGS
QUEUE_CONFIGS["hive_itd_model"]="12288:3072"      # 12288GB内存:3072vcores
QUEUE_CONFIGS["hive_itd_yx_234g"]="4096:1024"     # 4096GB内存:1024vcores
QUEUE_CONFIGS["hive_itd_yx_com"]="2048:512"       # 2048GB内存:512vcores
QUEUE_CONFIGS["hive_itd_yx_dw"]="512:128"         # 512GB内存:128vcores
QUEUE_CONFIGS["hive_itd_yx_dwd"]="1024:256"       # 1024GB内存:256vcores
QUEUE_CONFIGS["hive_itd_yx_ods"]="1536:384"       # 1024GB内存:256vcores (默认配置)
QUEUE_CONFIGS["hive_itd_yx_st"]="512:128"         # 512GB内存:128vcores
QUEUE_CONFIGS["hive_itd_yx_vb8"]="20480:5120"     # 20480GB内存:5120vcores
QUEUE_CONFIGS["hive_itd_bonc_zydc"]="20480:2500"  # 20480GB内存:2500vcores
QUEUE_CONFIGS["yn_lakehouse"]="7000:3500"        # 7000GB内存:3500vcores

# 当前运行的应用信息（支持多队列）
declare -A CURRENT_APPLICATIONS  # 队列名 -> Application ID
declare -A CURRENT_SPARK_LOGS    # 队列名 -> 日志文件路径
APPLICATION_DATA_DIR="$(dirname "$0")/app_data"

# 时间配置 (分钟)
MIN_DURATION=50    # 最短运行时间
MAX_DURATION=60    # 最长运行时间
MIN_INTERVAL=1     # 最短间隔时间
MAX_INTERVAL=3    # 最长间隔时间

# 多队列并发管理
declare -A QUEUE_START_TIMES    # 队列启动时间
declare -A QUEUE_DURATIONS      # 队列运行时长
declare -A QUEUE_NEXT_START     # 队列下次启动时间

# ==================== 工具函数 ====================

# 日志函数
log() {
    local level=$1
    shift
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $*" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
}

log_warn() {
    log "WARN" "$@"
}

log_error() {
    log "ERROR" "$@"
}

# 检查并创建日志目录
init_log() {
    if [[ ! -d "$LOG_DIR" ]]; then
        mkdir -p "$LOG_DIR"
        log_info "创建日志目录: $LOG_DIR"
    fi

    # 检查并创建应用数据目录
    if [[ ! -d "$APPLICATION_DATA_DIR" ]]; then
        mkdir -p "$APPLICATION_DATA_DIR"
        log_info "创建应用数据目录: $APPLICATION_DATA_DIR"
    fi
}

# 生成随机数
random_between() {
    local min=$1
    local max=$2
    echo $((RANDOM % (max - min + 1) + min))
}

# 随机选择队列
get_random_queue() {
    local queues=($(printf '%s\n' "${!QUEUE_CONFIGS[@]}"))
    local index=$(random_between 0 $((${#queues[@]} - 1)))
    echo "${queues[$index]}"
}

# 根据队列内存、CPU vcores和资源级别计算executor配置
calculate_executor_config() {
    local queue_config=$1
    local resource_level=$2

    # 解析队列配置: memory:vcores
    IFS=':' read -r queue_memory_gb queue_vcores <<< "$queue_config"

    # 如果没有vcores配置，使用默认值（内存GB数的1/4）
    if [[ -z "$queue_vcores" ]]; then
        queue_vcores=$((queue_memory_gb / 4))
    fi

    # 计算可用资源 (70%-90%的队列资源)
    local available_memory_gb=$((queue_memory_gb * resource_level / 100))
    local available_vcores=$((queue_vcores * resource_level / 100))

    # 根据内存大小决定executor配置策略
    local num_executors executor_cores executor_memory

    # 针对641个节点集群优化的配置策略
    # 目标：精确达到指定level的资源利用率，同时考虑内存和CPU约束
    local executor_memory_num
    
    if [[ $available_memory_gb -ge 10000 ]]; then
        # 超大队列（≥10TB）：使用12GB executor，确保能充分利用内存
        executor_memory="12g"
        executor_memory_num=12
        executor_cores=3
        num_executors=$((available_memory_gb / executor_memory_num))
    elif [[ $available_memory_gb -ge 5000 ]]; then
        # 大队列（≥5TB）：使用10GB executor，平衡性能与数量
        executor_memory="10g"
        executor_memory_num=10
        executor_cores=3
        num_executors=$((available_memory_gb / executor_memory_num))
    elif [[ $available_memory_gb -ge 2000 ]]; then
        # 中等队列（≥2TB）：使用8GB executor，标准配置
        executor_memory="8g"
        executor_memory_num=8
        executor_cores=3
        num_executors=$((available_memory_gb / executor_memory_num))
    elif [[ $available_memory_gb -ge 1000 ]]; then
        # 较小队列（≥1TB）：使用6GB executor，轻量配置
        executor_memory="6g"
        executor_memory_num=6
        executor_cores=2
        num_executors=$((available_memory_gb / executor_memory_num))
    else
        # 小队列（<1TB）：使用4GB executor，最小配置
        executor_memory="4g"
        executor_memory_num=4
        executor_cores=2
        num_executors=$((available_memory_gb / executor_memory_num))
    fi

    # 应用CPU vcores约束，确保两种资源都最大化利用
    local max_executors_by_vcores=$((available_vcores / executor_cores))

    # 双重约束优化：确保内存和CPU都达到高利用率
    if [[ $num_executors -gt $max_executors_by_vcores ]]; then
        # 场景1：内存充足但CPU不足，受CPU约束
        # 在CPU约束下，调整executor内存配置以最大化内存利用
        num_executors=$max_executors_by_vcores
        local optimized_memory=$((available_memory_gb / num_executors))

        # 确保优化后的内存在合理范围内（最小4GB，最大32GB）
        if [[ $optimized_memory -ge 4 && $optimized_memory -le 32 ]]; then
            executor_memory="${optimized_memory}g"
            executor_memory_num=$optimized_memory
        else
            # 如果计算出的内存超出合理范围，使用边界值
            if [[ $optimized_memory -lt 4 ]]; then
                executor_memory="4g"
                executor_memory_num=4
            else
                executor_memory="32g"
                executor_memory_num=32
            fi
        fi

    elif [[ $max_executors_by_vcores -gt $num_executors ]]; then
        # 场景2：CPU充足但内存不足，受内存约束
        # 在内存约束下，尝试调整配置以更好利用CPU

        # 方案A：增加executor数量，减少每个executor内存
        local target_executors=$max_executors_by_vcores
        local adjusted_memory=$((available_memory_gb / target_executors))

        if [[ $adjusted_memory -ge 4 && $adjusted_memory -le 32 ]]; then
            # 可以通过调整内存来利用更多CPU
            executor_memory="${adjusted_memory}g"
            executor_memory_num=$adjusted_memory
            num_executors=$target_executors
        else
            # 方案B：保持当前executor数量，尝试增加每个executor的核心数
            # 计算每个executor可以分配的最大核心数
            local max_cores_per_executor=$((available_vcores / num_executors))
            # 允许更高的核心数以充分利用CPU资源，但不超过12核心
            if [[ $max_cores_per_executor -gt $executor_cores && $max_cores_per_executor -le 12 ]]; then
                executor_cores=$max_cores_per_executor
            else
                # 方案C：在内存约束下，尝试找到最优的executor配置
                # 减少每个executor的内存，在合理范围内增加executor数量
                local min_memory=4
                local max_possible_executors=$((available_memory_gb / min_memory))
                local cpu_limited_executors=$((available_vcores / executor_cores))

                # 选择较小值作为目标executor数量
                local optimized_executors
                if [[ $max_possible_executors -lt $cpu_limited_executors ]]; then
                    optimized_executors=$max_possible_executors
                else
                    optimized_executors=$cpu_limited_executors
                fi

                # 如果优化后的executor数量比当前多，则采用
                if [[ $optimized_executors -gt $num_executors ]]; then
                    local optimized_memory=$((available_memory_gb / optimized_executors))
                    if [[ $optimized_memory -ge 4 ]]; then
                        num_executors=$optimized_executors
                        executor_memory="${optimized_memory}g"
                        executor_memory_num=$optimized_memory
                    fi
                fi
            fi
        fi
    fi

    # 最终验证：确保不超过任何资源限制
    local final_memory_usage=$((num_executors * executor_memory_num))
    local final_vcores_usage=$((num_executors * executor_cores))

    if [[ $final_memory_usage -gt $available_memory_gb ]]; then
        # 内存超限，重新调整
        num_executors=$((available_memory_gb / executor_memory_num))
    fi

    if [[ $final_vcores_usage -gt $available_vcores ]]; then
        # CPU超限，重新调整
        num_executors=$((available_vcores / executor_cores))
    fi

    # 应用CPU vcores约束
    local max_executors_by_vcores=$((available_vcores / executor_cores))
    if [[ $num_executors -gt $max_executors_by_vcores ]]; then
        num_executors=$max_executors_by_vcores
    fi
    
    # 确保executor数量在合理范围内，但不限制大队列的资源使用
    # 641个节点集群可以支持大量executor（假设每节点可运行3-5个executor）
    local max_reasonable_executors=$((641 * 4))  # 约2,500个executor

    if [[ $num_executors -lt 5 ]]; then
        num_executors=5
    elif [[ $num_executors -gt $max_reasonable_executors ]]; then
        # 如果计算出的executor数量过多，调整executor内存大小
        # 重新计算以确保不超过合理限制
        local adjusted_memory=$((available_memory_gb / max_reasonable_executors + 1))
        if [[ $adjusted_memory -lt 4 ]]; then
            adjusted_memory=4
        elif [[ $adjusted_memory -gt 32 ]]; then
            adjusted_memory=32
        fi
        executor_memory="${adjusted_memory}g"
        num_executors=$((available_memory_gb / adjusted_memory))

        # 确保不超过最大限制
        if [[ $num_executors -gt $max_reasonable_executors ]]; then
            num_executors=$max_reasonable_executors
        fi
    fi

    # 计算实际使用的资源
    local used_memory=$((num_executors * executor_memory_num))
    local used_vcores=$((num_executors * executor_cores))

    # 返回格式: executors:cores:memory:used_memory:used_vcores:available_memory:available_vcores
    echo "${num_executors}:${executor_cores}:${executor_memory}:${used_memory}:${used_vcores}:${available_memory_gb}:${available_vcores}"
}

# 随机选择资源级别
get_random_resource_level() {
    local levels=(70 75 80 85 90)
    local index=$(random_between 0 4)
    echo "${levels[$index]}"
}

# 启动所有队列的任务
start_all_queues() {
    log_info "=== 启动所有队列的任务 ==="
    local current_time=$(date +%s)
    
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        # 检查该队列是否需要启动
        if should_start_queue "$queue" "$current_time"; then
            local resource_level
            resource_level=$(get_random_resource_level)
            local duration
            duration=$(random_between $MIN_DURATION $MAX_DURATION)
            
            log_info "启动队列 $queue，资源级别: ${resource_level}%，运行时长: ${duration}分钟"
            
            if start_spark_task "$queue" "$resource_level"; then
                QUEUE_START_TIMES["$queue"]="$current_time"
                QUEUE_DURATIONS["$queue"]="$duration"
                log_info "队列 $queue 已成功启动"
            else
                log_error "队列 $queue 启动失败"
            fi
        fi
    done
}

# 检查队列是否需要启动
should_start_queue() {
    local queue=$1
    local current_time=$2
    
    # 如果队列已在运行，不需要启动
    if [[ -n "${CURRENT_APPLICATIONS[$queue]}" ]]; then
        return 1
    fi
    
    # 如果是首次启动或已到达下次启动时间
    if [[ -z "${QUEUE_NEXT_START[$queue]}" ]] || [[ $current_time -ge ${QUEUE_NEXT_START[$queue]} ]]; then
        return 0
    fi
    
    return 1
}

# 检查并停止到期的队列任务
check_and_stop_expired_queues() {
    local current_time=$(date +%s)
    
    for queue in "${!CURRENT_APPLICATIONS[@]}"; do
        if [[ -n "${QUEUE_START_TIMES[$queue]}" ]] && [[ -n "${QUEUE_DURATIONS[$queue]}" ]]; then
            local start_time=${QUEUE_START_TIMES[$queue]}
            local duration_seconds=$((${QUEUE_DURATIONS[$queue]} * 60))
            local end_time=$((start_time + duration_seconds))
            
            if [[ $current_time -ge $end_time ]]; then
                log_info "队列 $queue 运行时间已到期，正在停止..."
                stop_queue_spark "$queue"
                
                # 计算下次启动时间（加上间隔时间）
                local interval
                interval=$(random_between $MIN_INTERVAL $MAX_INTERVAL)
                local next_start=$((current_time + interval * 60))
                QUEUE_NEXT_START["$queue"]="$next_start"
                
                # 清理启动时间记录
                unset QUEUE_START_TIMES["$queue"]
                unset QUEUE_DURATIONS["$queue"]
                
                log_info "队列 $queue 将在 ${interval} 分钟后重新启动"
            fi
        fi
    done
}

# 显示所有队列状态
show_all_queue_status() {
    local current_time=$(date +%s)
    
    log_info "=== 所有队列状态概览 ==="
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        if [[ -n "${CURRENT_APPLICATIONS[$queue]}" ]]; then
            local start_time=${QUEUE_START_TIMES[$queue]}
            local duration=${QUEUE_DURATIONS[$queue]}
            local running_time=$(( (current_time - start_time) / 60 ))
            local remaining_time=$((duration - running_time))
            
            if [[ $remaining_time -gt 0 ]]; then
                log_info "队列 $queue: 运行中 (已运行${running_time}分钟，剩余${remaining_time}分钟)"
            else
                log_info "队列 $queue: 运行中 (即将停止)"
            fi
        else
            if [[ -n "${QUEUE_NEXT_START[$queue]}" ]]; then
                local next_start_time=${QUEUE_NEXT_START[$queue]}
                local wait_time=$(( (next_start_time - current_time) / 60 ))
                if [[ $wait_time -gt 0 ]]; then
                    log_info "队列 $queue: 等待中 (${wait_time}分钟后启动)"
                else
                    log_info "队列 $queue: 准备启动"
                fi
            else
                log_info "队列 $queue: 准备启动"
            fi
        fi
    done
}

# 检查关键程序是否存在
check_dependencies() {
    local missing_deps=()
    
    if [[ ! -f "$SPARK_SQL_BIN" ]]; then
        missing_deps+=("Spark SQL: $SPARK_SQL_BIN")
    fi
    
    if [[ ! -f "$YARN_BIN" ]]; then
        missing_deps+=("YARN: $YARN_BIN")
    fi
    
    if [[ ! -f "$KEYTAB" ]]; then
        missing_deps+=("Keytab: $KEYTAB")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_error "缺少依赖文件:"
        for dep in "${missing_deps[@]}"; do
            log_error "  - $dep"
        done
        return 1
    fi
    
    return 0
}

# 检查应用状态
check_application_status() {
    local app_id=$1
    if [[ -z "$app_id" ]]; then
        echo "UNKNOWN"
        return
    fi
    
    # 使用yarn命令获取应用状态，加上超时保护
    local status
    status=$(timeout 8 $YARN_BIN application -status "$app_id" 2>/dev/null | grep -E "^\s*State" | awk '{print $3}')
    
    # 如果没有获取到状态，尝试获取Final-State
    if [[ -z "$status" ]]; then
        status=$(timeout 8 $YARN_BIN application -status "$app_id" 2>/dev/null | grep "Final-State" | awk '{print $3}')
    fi
    
    echo "${status:-UNKNOWN}"
}



# 查找指定队列的所有运行中的应用
find_queue_applications() {
    local queue=$1
    
    # 通过应用名称查找所有相关的应用ID
    local app_ids=()
    local app_list
    
    # 使用timeout防止yarn命令卡住
    app_list=$(timeout 30 $YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
               grep "DynamicQueueManager-${queue}" | awk '{print $1}' || true)
    
    if [[ -n "$app_list" ]]; then
        while IFS= read -r app_id; do
            if [[ -n "$app_id" && "$app_id" =~ ^application_ ]]; then
                app_ids+=("$app_id")
            fi
        done <<< "$app_list"
    fi
    
    echo "${app_ids[@]}"
}



# 停止指定队列的Spark应用
stop_queue_spark() {
    local queue=$1
    
    log_info "开始停止队列 $queue 的所有Spark应用"
    
    # 首先通过应用名称查找所有相关应用
    local app_ids
    app_ids=($(find_queue_applications "$queue"))
    
    # 如果本地记录的应用ID存在但不在YARN中，说明可能被其他人杀掉了
    if [[ -n "${CURRENT_APPLICATIONS[$queue]}" ]]; then
        local recorded_app_id="${CURRENT_APPLICATIONS[$queue]}"
        local found_in_yarn=false
        
        for app_id in "${app_ids[@]}"; do
            if [[ "$app_id" == "$recorded_app_id" ]]; then
                found_in_yarn=true
                break
            fi
        done
        
        if [[ "$found_in_yarn" == false ]]; then
            log_warn "队列 $queue 本地记录的应用 $recorded_app_id 不在YARN中，可能已被其他人终止"
            # 清理本地记录
            unset CURRENT_APPLICATIONS["$queue"]
            unset CURRENT_SPARK_LOGS["$queue"]
            rm -f "$APPLICATION_DATA_DIR/${queue}_app_id"
            rm -f "$APPLICATION_DATA_DIR/${queue}_log_path"
        fi
    fi
    
    # 如果没有找到任何应用
    if [[ ${#app_ids[@]} -eq 0 ]]; then
        log_info "队列 $queue 没有运行中的应用"
        # 清理可能遗留的本地记录
        unset CURRENT_APPLICATIONS["$queue"]
        unset CURRENT_SPARK_LOGS["$queue"]
        rm -f "$APPLICATION_DATA_DIR/${queue}_app_id"
        rm -f "$APPLICATION_DATA_DIR/${queue}_log_path"
        
        # 清理SQL文件
        local sql_file="$(dirname "$0")/spark_task_${queue}.sql"
        if [[ -f "$sql_file" ]]; then
            rm -f "$sql_file"
            log_info "已清理队列 $queue 的SQL文件"
        fi
        return 0
    fi
    
    log_info "发现队列 $queue 有 ${#app_ids[@]} 个运行中的应用: ${app_ids[*]}"
    
    # 逐个终止所有应用
    local killed_count=0
    for app_id in "${app_ids[@]}"; do
        log_info "正在终止队列 $queue 的应用: $app_id"
        
        # 首先尝试通过app_id杀掉
        if timeout 30 $YARN_BIN application -kill "$app_id" > /dev/null 2>&1; then
            log_info "通过Application ID成功发送终止命令: $app_id"
            ((killed_count++))
        else
            log_warn "通过Application ID终止失败: $app_id，尝试通过应用名称终止"
            
            # 通过应用名称强制终止（备用方案）
            local app_name_pattern="DynamicQueueManager-${queue}"
            local kill_by_name_result
            kill_by_name_result=$(timeout 30 $YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
                                  grep "$app_name_pattern" | grep "$app_id" | awk '{print $1}' | \
                                  xargs -I {} timeout 30 $YARN_BIN application -kill {} 2>&1 || true)
            
            if [[ $? -eq 0 ]]; then
                log_info "通过应用名称成功终止应用: $app_id"
                ((killed_count++))
            else
                log_error "无法终止应用 $app_id，可能需要手动处理"
            fi
        fi
        
        # 等待应用真正被终止
        local count=0
        local app_status=""
        while [[ $count -lt 15 ]]; do
            app_status=$(timeout 8 $YARN_BIN application -status "$app_id" 2>/dev/null | grep -E "^\s*State|Final-State" | awk '{print $3}' | head -1)
            
            if [[ "$app_status" == "KILLED" || "$app_status" == "FAILED" || "$app_status" == "FINISHED" ]]; then
                log_info "应用 $app_id 已成功终止，状态: $app_status"
                break
            elif [[ -z "$app_status" ]]; then
                log_warn "应用 $app_id 已不存在，可能已被终止"
                break
            else
                log_info "等待应用 $app_id 终止中，当前状态: $app_status (${count}/15)"
                sleep 2
                ((count++))
            fi
        done
        
        if [[ $count -ge 15 ]]; then
            log_warn "应用 $app_id 终止超时，当前状态: $app_status"
        fi
    done
    
    log_info "队列 $queue 总共处理了 ${#app_ids[@]} 个应用，成功终止 $killed_count 个"
    
    # 清理应用信息
    unset CURRENT_APPLICATIONS["$queue"]
    unset CURRENT_SPARK_LOGS["$queue"]
    rm -f "$APPLICATION_DATA_DIR/${queue}_app_id"
    rm -f "$APPLICATION_DATA_DIR/${queue}_log_path"
    
    # 清理SQL文件
    local sql_file="$(dirname "$0")/spark_task_${queue}.sql"
    if [[ -f "$sql_file" ]]; then
        rm -f "$sql_file"
        log_info "已清理队列 $queue 的SQL文件"
    fi
}

# 停止所有运行中的应用
stop_all_applications() {
    log_info "停止所有运行中的Spark应用"
    
    for queue in "${!CURRENT_APPLICATIONS[@]}"; do
        stop_queue_spark "$queue"
    done
    
    # 清理旧的Spark日志文件（保留最近的10个）
    cleanup_old_spark_logs
}

# 启动Spark SQL任务
start_spark_task() {
    local queue=$1
    local level=$2
    
    # 检查队列配置
    if [[ -z "${QUEUE_CONFIGS[$queue]}" ]]; then
        log_error "无效的队列: $queue"
        return 1
    fi
    
    # 检查队列是否已有运行中的应用（防御性检查）
    local existing_apps=($(find_queue_applications "$queue"))
    if [[ ${#existing_apps[@]} -gt 0 ]]; then
        log_warn "发现队列 $queue 仍有 ${#existing_apps[@]} 个运行中的应用，先清理"
        stop_queue_spark "$queue"
        sleep 3  # 等待清理完成
    fi
    
    # 计算资源配置
    local queue_config=${QUEUE_CONFIGS[$queue]}
    local config
    config=$(calculate_executor_config "$queue_config" "$level")

    # 解析配置: executors:cores:memory:used_memory:used_vcores:available_memory:available_vcores
    IFS=':' read -r num_executors executor_cores executor_memory used_memory used_vcores available_memory available_vcores <<< "$config"

    # 解析队列配置获取总资源
    IFS=':' read -r queue_memory_gb queue_vcores <<< "$queue_config"

    log_info "启动队列 $queue Spark应用 - 资源级别: ${level}%"
    log_info "队列总资源: ${queue_memory_gb}GB内存, ${queue_vcores}vcores"
    log_info "可用资源: ${available_memory}GB内存, ${available_vcores}vcores"
    log_info "配置详情: ${num_executors}执行器, ${executor_cores}核心/执行器, ${executor_memory}内存/执行器"
    log_info "实际使用: ${used_memory}GB内存 (${used_memory}/${queue_memory_gb}), ${used_vcores}vcores (${used_vcores}/${queue_vcores})"
    
    # 在程序目录创建队列专用SQL文件
    local temp_sql_file="$(dirname "$0")/spark_task_${queue}.sql"
    cat > "$temp_sql_file" << EOF
-- 显示任务启动信息
SELECT '=== 队列资源无限占用任务启动 ===' as task_info;
SELECT '队列名称: ${queue}' as queue_name;
SELECT '队列总内存: ${queue_memory_gb}GB' as queue_total_memory;
SELECT '队列总vcores: ${queue_vcores}' as queue_total_vcores;
SELECT '资源级别: ${level}%' as resource_level;
SELECT '可用内存: ${available_memory}GB' as available_memory;
SELECT '可用vcores: ${available_vcores}' as available_vcores;
SELECT '执行器数量: ${num_executors}' as executors;
SELECT '执行器核心: ${executor_cores}' as cores_per_executor;
SELECT '执行器内存: ${executor_memory}' as memory_per_executor;
SELECT '实际使用内存: ${used_memory}GB' as used_memory;
SELECT '实际使用vcores: ${used_vcores}' as used_vcores;
SELECT '内存利用率: ${used_memory}/${queue_memory_gb}GB' as memory_utilization;
SELECT 'vcores利用率: ${used_vcores}/${queue_vcores}' as vcores_utilization;
SELECT '开始时间: ', current_timestamp() as start_time;

-- 超大数据集无限处理 - 这个查询会持续运行直到被kill
-- 使用巨大的数字范围确保永远执行不完
SELECT 
    '无限循环资源占用中...' as status,
    count(*) as total_processed,
    sum(a.id * b.id * c.id) as triple_product_sum,
    avg(a.id + b.id + c.id) as avg_sum,
    max(a.id * a.id + b.id * b.id + c.id * c.id) as max_squares_sum,
    stddev(a.id * b.id + b.id * c.id + c.id * a.id) as stddev_mixed,
    count(distinct concat('combo_', a.id, '_', b.id, '_', c.id)) as unique_combinations,
    percentile_approx(a.id * b.id * c.id, 0.5) as median_product,
    current_timestamp() as processing_time
FROM 
    (SELECT explode(sequence(1, 100000000)) as id) a
CROSS JOIN 
    (SELECT explode(sequence(1, 100000000)) as id) b  
CROSS JOIN
    (SELECT explode(sequence(1, 100000000)) as id) c
WHERE 
    a.id % 1000 = 0 
    AND b.id % 1000 = 0 
    AND c.id % 1000 = 0
    AND a.id <= b.id 
    AND b.id <= c.id
    AND (a.id + b.id + c.id) % 5000 = 0;
EOF
    
    # 构建Spark SQL命令
    local spark_cmd="$SPARK_SQL_BIN \
        --master $MASTER \
        --deploy-mode $DEPLOY_MODE \
        --queue $queue \
        --driver-memory $DRIVER_MEMORY \
        --num-executors $num_executors \
        --executor-cores $executor_cores \
        --executor-memory $executor_memory \
        --principal $PRINCIPAL \
        --keytab $KEYTAB \
        --name \"DynamicQueueManager-${queue}-Level${level}%-\$(date +%H%M%S)\" \
        --conf spark.driver.maxResultSize=2g \
        --conf spark.executor.memoryOverhead=4g \
        --conf spark.default.parallelism=480 \
        --conf spark.sql.shuffle.partitions=480 \
        --conf spark.shuffle.file.buffer=64k \
        --conf spark.reducer.maxSizeInFlight=96m \
        --conf spark.sql.tungsten.enabled=true \
        --conf spark.io.compression.codec=snappy \
        --conf spark.rdd.compress=true \
        --conf spark.serializer=org.apache.spark.serializer.KryoSerializer \
        --conf spark.sql.adaptive.enabled=true \
        --conf spark.sql.adaptive.shuffle.targetPostShuffleInputSize=256m \
        --conf spark.sql.adaptive.join.enabled=true \
        --conf spark.sql.adaptive.allowAdditionalShuffle=true \
        --conf spark.sql.adaptive.skewedJoin.enabled=true \
        --conf spark.sql.adaptive.skewedPartitionMaxSplits=10 \
        --conf spark.sql.adaptive.skewedPartitionSizeThreshold=64 \
        --conf spark.driver.extraJavaOptions=\"-Djava.security.krb5.conf=/home/<USER>/weizm/krb5.conf\" \
        -f \"$temp_sql_file\""
    
    # 启动Spark应用，日志输出到文件
    local spark_log_file="$(dirname "$0")/spark_${queue}_${level}_$(date +%Y%m%d_%H%M%S).log"
    log_info "启动队列 $queue Spark应用，日志保存到: $(basename "$spark_log_file")"
    
    # 启动Spark应用，输出重定向到日志文件
    eval "$spark_cmd" > "$spark_log_file" 2>&1 &
    local spark_submit_pid=$!
    
    # 检查命令是否启动成功
    if [[ -z "$spark_submit_pid" ]]; then
        log_error "队列 $queue Spark SQL 命令启动失败"
        rm -f "$temp_sql_file"
        return 1
    fi
    
    log_info "队列 $queue Spark应用已在后台启动 (PID: $spark_submit_pid)"
    
    # 等待应用提交并获取Application ID
    local app_id=""
    local count=0
    while [[ $count -lt 60 ]] && [[ -z "$app_id" ]]; do
        sleep 3
        
        # 通过yarn命令获取最新的应用ID
        app_id=$($YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
                grep "DynamicQueueManager-${queue}-Level${level}%" | \
                tail -1 | awk '{print $1}')
        
        # 检查Spark进程是否还在运行
        if ! kill -0 $spark_submit_pid 2>/dev/null; then
            log_warn "队列 $queue Spark提交进程已退出，但可能应用已成功提交"
            # 再次尝试获取应用ID
            app_id=$($YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
                    grep "DynamicQueueManager-${queue}-Level${level}%" | \
                    tail -1 | awk '{print $1}')
            if [[ -n "$app_id" ]]; then
                log_info "队列 $queue 应用已成功提交"
                break
            else
                log_error "队列 $queue Spark应用提交失败"
                rm -f "$temp_sql_file" "$spark_log_file"
                return 1
            fi
        fi
        
        ((count++))
        
        if [[ $count -eq 20 ]]; then
            log_info "正在等待队列 $queue 应用提交完成... (${count}/60)"
        elif [[ $count -eq 40 ]]; then
            log_info "队列 $queue 应用提交耗时较长，继续等待... (${count}/60)"
        fi
    done
    
    if [[ -n "$app_id" ]]; then
        # 保存应用信息
        CURRENT_APPLICATIONS["$queue"]="$app_id"
        CURRENT_SPARK_LOGS["$queue"]="$spark_log_file"
        
        # 持久化应用信息
        echo "$app_id" > "$APPLICATION_DATA_DIR/${queue}_app_id"
        echo "$spark_log_file" > "$APPLICATION_DATA_DIR/${queue}_log_path"
        
        log_info "队列 $queue Spark应用启动成功 (Application ID: $app_id)"
        log_info "队列 $queue 日志文件: $(basename "$spark_log_file")"
        
        # 验证应用状态
        sleep 5  # 等待应用完全启动
        local app_status
        app_status=$(check_application_status "$app_id")
        log_info "队列 $queue 应用当前状态: $app_status"
        
        return 0
    else
        log_error "无法获取队列 $queue Application ID，应用可能启动失败"
        # 启动失败时清理文件
        rm -f "$temp_sql_file" "$spark_log_file"
        return 1
    fi
}

# 清理旧的Spark日志文件
cleanup_old_spark_logs() {
    local log_dir="$(dirname "$0")"
    local spark_logs=("$log_dir"/spark_*.log)
    
    # 检查是否有日志文件
    if [[ ${#spark_logs[@]} -gt 0 && -f "${spark_logs[0]}" ]]; then
        # 按修改时间排序，获取文件数量
        local log_count=$(ls -1t "$log_dir"/spark_*.log 2>/dev/null | wc -l)
        
        if [[ $log_count -gt 3 ]]; then
            log_info "发现 $log_count 个Spark日志文件，清理旧文件（保留最新3个）"
            # 删除最旧的文件（跳过最新的3个）
            ls -1t "$log_dir"/spark_*.log 2>/dev/null | tail -n +4 | while read -r old_log; do
                rm -f "$old_log"
                log_info "已删除旧日志文件: $(basename "$old_log")"
            done
        fi
    fi
}

# 检查队列状态 (可选扩展功能)
check_queue_status() {
    # 检查队列中的应用状态
    log_info "检查队列状态..."
    
    # 获取队列中的应用列表
    local queue_apps
    queue_apps=$($YARN_BIN application -list -appStates RUNNING,ACCEPTED,SUBMITTED 2>/dev/null | \
                grep "$QUEUE" | wc -l)
    
    if [[ $queue_apps -gt 0 ]]; then
        log_info "队列 $QUEUE 中有 $queue_apps 个运行中的应用"
    fi
}

# 信号处理函数
cleanup() {
    log_info "接收到停止信号，正在清理..."
    
    # 1. 停止所有spark-sql进程
    log_info "停止所有spark-sql进程..."
    local spark_pids=$(ps aux | grep "DynamicQueueManager" | awk '{print $2}' || true)
    if [[ -n "$spark_pids" ]]; then
        echo "$spark_pids" | while read -r pid; do
            if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ ]]; then
                log_info "终止spark-sql进程: $pid"
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done
        
        # 等待3秒后强制终止未响应的进程
        sleep 3
        spark_pids=$(ps aux | grep "DynamicQueueManager" | awk '{print $2}' || true)
        if [[ -n "$spark_pids" ]]; then
            echo "$spark_pids" | while read -r pid; do
                if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ ]]; then
                    log_info "强制终止spark-sql进程: $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
        fi
    fi
    
    # 2. 清理YARN中的应用
    log_info "清理YARN应用..."
    stop_all_applications
    
    # 3. 清理所有队列状态
    log_info "清理所有队列状态..."
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        unset QUEUE_START_TIMES["$queue"]
        unset QUEUE_DURATIONS["$queue"]
        unset QUEUE_NEXT_START["$queue"]
    done
    
    rm -f "$PID_FILE"
    log_info "所有队列状态已清理，脚本已停止"
    exit 0
}

# 检查脚本是否已在运行
check_running() {
    if [[ -f "$PID_FILE" ]]; then
        local old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            log_error "脚本已在运行 (PID: $old_pid)"
            exit 1
        else
            log_warn "发现僵尸PID文件，清理中..."
            rm -f "$PID_FILE"
        fi
    fi
}

# 主循环 - 多队列并发运行
main_loop() {
    log_info "开始动态多队列并发资源管理"
    log_info "支持的队列: $(printf '%s ' "${!QUEUE_CONFIGS[@]}")"
    log_info "所有队列将同时运行，独立管理生命周期"
    
    # 初始启动所有队列
    start_all_queues
    
    local status_counter=0
    
    while true; do
        # 检查并停止到期的队列任务
        check_and_stop_expired_queues
        
        # 启动需要启动的队列
        start_all_queues
        
        # 每10个循环显示一次状态概览
        ((status_counter++))
        if [[ $((status_counter % 10)) -eq 0 ]]; then
            show_all_queue_status
        fi
        
        # 等待30秒后进行下一轮检查
        sleep 30
    done
}

# ==================== 主程序 ====================

# 脚本使用帮助
usage() {
    cat << EOF
使用方法: $0 [选项]

选项:
  start     启动动态队列资源管理器
  stop      停止动态队列资源管理器  
  restart   重启动态队列资源管理器
  status    查看运行状态
  logs      查看日志
  config    查看所有队列资源配置
  cleanup   手动清理遗留的YARN应用
  help      显示此帮助信息

示例:
  $0 start          # 启动服务
  $0 stop           # 停止服务  
  $0 restart        # 重启服务
  $0 status         # 查看状态
  $0 logs           # 查看日志
  $0 config         # 查看配置
  $0 cleanup        # 清理YARN应用

EOF
}

# 启动函数
start_service() {
    init_log
    
    # 检查脚本是否已在运行
    check_running
    
    # 立即写入PID文件，防止竞态条件
    echo $$ > "$PID_FILE"
    
    # 检查依赖文件
    if ! check_dependencies; then
        log_error "依赖检查失败，无法启动服务"
        rm -f "$PID_FILE"  # 清理PID文件
        exit 1
    fi
    
    log_info "启动动态多队列并发资源管理器..."
    log_info "将同时管理 ${#QUEUE_CONFIGS[@]} 个队列的资源使用"
    log_info "进程PID: $$"
    
    # 清理所有现有应用，确保干净启动
    log_info "清理所有现有YARN应用，确保干净启动..."
    cleanup_remaining_applications
    
    # 设置信号处理
    trap cleanup SIGTERM SIGINT SIGQUIT
    
    # 进入主循环
    main_loop
}

# 停止函数
stop_service() {
    local script_name=$(basename "$0")
    local script_basename="${script_name%.*}"  # 去掉扩展名
    local current_pid=$$
    
    echo "正在停止动态队列管理器服务..."
    
    # 1. 查找所有相关脚本进程（排除当前进程）
    echo "查找所有 ${script_name} 相关进程（排除当前进程 $current_pid）..."
    local all_script_pids=()
    while IFS= read -r line; do
        if [[ -n "$line" ]]; then
            local pid=$(echo "$line" | awk '{print $2}')
            if [[ -n "$pid" && "$pid" =~ ^[0-9]+$ && "$pid" != "$current_pid" ]]; then
                # 进一步检查确实是主服务进程（非stop子进程）
                local cmdline=$(ps -p "$pid" -o args= 2>/dev/null || true)
                if [[ "$cmdline" =~ ${script_name}[[:space:]]+start ]] || [[ "$cmdline" =~ ${script_name}$ ]] && [[ ! "$cmdline" =~ stop ]]; then
                    all_script_pids+=("$pid")
                fi
            fi
        fi
    done < <(ps aux | grep "${script_name}" | grep -v "grep" | grep -v "$$" || true)
    
    if [[ ${#all_script_pids[@]} -gt 0 ]]; then
        echo "发现 ${#all_script_pids[@]} 个 ${script_name} 主服务进程: ${all_script_pids[*]}"
        
        # 显示进程详情
        echo "进程详情:"
        ps -f -p $(IFS=,; echo "${all_script_pids[*]}") 2>/dev/null || true
        
        # 2. 首先尝试优雅关闭所有脚本进程
        echo "尝试优雅关闭所有脚本进程..."
        for script_pid in "${all_script_pids[@]}"; do
            if [[ "$script_pid" =~ ^[0-9]+$ ]] && kill -0 "$script_pid" 2>/dev/null; then
                echo "发送TERM信号给进程: $script_pid"
                kill -TERM "$script_pid" 2>/dev/null || true
            fi
        done
        
        # 等待进程退出
        echo "等待进程优雅退出..."
        local count=0
        while [[ $count -lt 15 ]]; do
            local remaining_pids=($(ps aux | grep "${script_name}" | grep -v "grep" | grep -v "$$" | awk '{print $2}' || true))
            if [[ ${#remaining_pids[@]} -eq 0 ]]; then
                echo "所有脚本进程已优雅退出"
                break
            fi
            sleep 1
            ((count++))
        done
        
        # 3. 如果还有进程未退出，强制终止
        local remaining_pids=($(ps aux | grep "${script_name}" | grep -v "grep" | grep -v "$$" | awk '{print $2}' || true))
        if [[ ${#remaining_pids[@]} -gt 0 ]]; then
            echo "强制终止剩余的 ${#remaining_pids[@]} 个脚本进程: ${remaining_pids[*]}"
            for script_pid in "${remaining_pids[@]}"; do
                if [[ "$script_pid" =~ ^[0-9]+$ ]] && kill -0 "$script_pid" 2>/dev/null; then
                    echo "强制终止进程: $script_pid"
                    kill -KILL "$script_pid" 2>/dev/null || true
                fi
            done
            sleep 2
        fi
    else
        echo "没有发现运行中的 ${script_name} 进程"
    fi
    
    # 4. 清理所有DynamicQueueManager相关的Spark进程
    echo "清理所有DynamicQueueManager相关的Spark进程..."
    local spark_pids=($(ps aux | grep "DynamicQueueManager" | grep -v "grep" | grep -v "$$" | grep -v "stop" | awk '{print $2}' || true))
    if [[ ${#spark_pids[@]} -gt 0 ]]; then
        echo "发现 ${#spark_pids[@]} 个DynamicQueueManager相关的Spark进程: ${spark_pids[*]}"
        
        # 先尝试优雅终止
        for spark_pid in "${spark_pids[@]}"; do
            if [[ "$spark_pid" =~ ^[0-9]+$ ]] && kill -0 "$spark_pid" 2>/dev/null; then
                echo "终止Spark进程: $spark_pid"
                kill -TERM "$spark_pid" 2>/dev/null || true
            fi
        done
        
        # 等待3秒后强制终止
        sleep 3
        spark_pids=($(ps aux | grep "DynamicQueueManager" | grep -v "grep" | grep -v "$$" | grep -v "stop" | awk '{print $2}' || true))
        if [[ ${#spark_pids[@]} -gt 0 ]]; then
            echo "强制终止剩余的 ${#spark_pids[@]} 个Spark进程: ${spark_pids[*]}"
            for spark_pid in "${spark_pids[@]}"; do
                if [[ "$spark_pid" =~ ^[0-9]+$ ]] && kill -0 "$spark_pid" 2>/dev/null; then
                    echo "强制终止Spark进程: $spark_pid"
                    kill -KILL "$spark_pid" 2>/dev/null || true
                fi
            done
        fi
    else
        echo "没有发现DynamicQueueManager相关的Spark进程"
    fi
    
    # 5. 处理PID文件
    if [[ -f "$PID_FILE" ]]; then
        local recorded_pid=$(cat "$PID_FILE")
        echo "清理PID文件 (记录的PID: $recorded_pid)"
        rm -f "$PID_FILE"
    fi
    
    # 6. 清理YARN应用
    echo "清理YARN应用..."
    cleanup_remaining_applications
  
    
    echo "动态队列管理器服务已停止"
}

# 清理遗留的YARN应用（独立于主进程运行）
cleanup_remaining_applications() {
    echo "正在加载应用信息..."
    
    # 初始化必要的配置
    init_log 2>/dev/null || true
    
    # 重新加载应用信息
    declare -A CLEANUP_APPLICATIONS
    init_application_data 2>/dev/null || true
    
    local cleaned_count=0
    
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        echo "检查队列 $queue 的所有应用..."
        
        # 通过应用名称查找所有相关应用
        local yarn_apps
        yarn_apps=($(timeout 30 $YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
                     grep "DynamicQueueManager-${queue}" | awk '{print $1}' || true))
        
        if [[ ${#yarn_apps[@]} -gt 0 ]]; then
            echo "发现队列 $queue 有 ${#yarn_apps[@]} 个YARN应用: ${yarn_apps[*]}"
            
            for app_id in "${yarn_apps[@]}"; do
                echo "正在终止队列 $queue 的应用: $app_id"
                
                # 检查应用状态
                local status
                status=$(timeout 8 $YARN_BIN application -status "$app_id" 2>/dev/null | grep -E "^\s*State" | awk '{print $3}')
                
                if [[ "$status" == "RUNNING" || "$status" == "ACCEPTED" || "$status" == "SUBMITTED" ]]; then
                    if timeout 30 $YARN_BIN application -kill "$app_id" > /dev/null 2>&1; then
                        echo "成功发送终止命令给应用: $app_id (状态: $status)"
                        ((cleaned_count++))
                    else
                        echo "警告: 终止应用 $app_id 失败，尝试通过应用名称强制终止"
                        
                        # 通过应用名称强制终止（备用方案）
                        local kill_by_name_result
                        kill_by_name_result=$(timeout 30 $YARN_BIN application -list -appStates SUBMITTED,ACCEPTED,RUNNING 2>/dev/null | \
                                              grep "DynamicQueueManager-${queue}" | grep "$app_id" | awk '{print $1}' | \
                                              xargs -I {} timeout 30 $YARN_BIN application -kill {} 2>&1 || true)
                        
                        if [[ $? -eq 0 ]]; then
                            echo "通过应用名称成功终止应用: $app_id"
                            ((cleaned_count++))
                        else
                            echo "错误: 无法终止应用 $app_id，可能需要手动处理"
                        fi
                    fi
                else
                    echo "队列 $queue 应用 $app_id 已结束 (状态: ${status:-UNKNOWN})"
                fi
            done
        fi
        
        # 清理本地文件
        local app_file="$APPLICATION_DATA_DIR/${queue}_app_id"
        local log_file="$APPLICATION_DATA_DIR/${queue}_log_path"
        if [[ -f "$app_file" ]]; then
            rm -f "$app_file"
            echo "已清理队列 $queue 的本地应用记录"
        fi
        if [[ -f "$log_file" ]]; then
            rm -f "$log_file"
        fi
        
        # 清理SQL文件
        local sql_file="$(dirname "$0")/spark_task_${queue}.sql"
        if [[ -f "$sql_file" ]]; then
            rm -f "$sql_file"
            echo "已清理队列 $queue 的SQL文件"
        fi
    done
    
    if [[ $cleaned_count -gt 0 ]]; then
        echo "已清理 $cleaned_count 个YARN应用"
    else
        echo "没有发现需要清理的YARN应用"
    fi
}

# 查看状态
show_status() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            echo "动态队列管理器正在运行 (PID: $pid)"
            
            # 实时查询当前应用信息（不依赖本地缓存）
            echo "正在查询当前运行状态..."
            
            # 实时显示当前Spark应用状态
            local running_queues=0
            echo "当前运行中的Spark应用:"
            
            for queue in "${!QUEUE_CONFIGS[@]}"; do
                local app_ids=($(find_queue_applications "$queue"))
                if [[ ${#app_ids[@]} -gt 0 ]]; then
                    ((running_queues++))
                    for app_id in "${app_ids[@]}"; do
                        local app_status
                        app_status=$(check_application_status "$app_id")
                        echo "  队列 $queue (${QUEUE_CONFIGS[$queue]}GB): $app_id (状态: $app_status)"
                    done
                fi
            done
            
            if [[ $running_queues -eq 0 ]]; then
                echo "  暂无运行中的应用"
            else
                echo "  共 $running_queues 个队列有运行中的应用"
            fi
            
            # 显示所有队列使用情况
            echo "所有队列使用情况:"
            for queue in "${!QUEUE_CONFIGS[@]}"; do
                local queue_config=${QUEUE_CONFIGS[$queue]}
                IFS=':' read -r queue_memory queue_vcores <<< "$queue_config"
                local queue_apps
                queue_apps=$($YARN_BIN application -list -appStates RUNNING,ACCEPTED,SUBMITTED 2>/dev/null | \
                           grep "$queue" | wc -l)
                echo "  $queue (${queue_memory}GB内存/${queue_vcores}vcores): $queue_apps 个运行中的应用"
            done
            
        else
            echo "管理器未运行 (僵尸PID文件存在)"
        fi
    else
        echo "动态队列管理器未运行"
    fi
}

# 查看日志
show_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        tail -f "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 显示所有队列的资源配置
show_queue_configs() {
    echo "=== 所有队列资源配置预览（641个节点集群优化版）==="
    echo ""

    local total_cluster_memory=0
    local total_cluster_vcores=0
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        IFS=':' read -r queue_memory queue_vcores <<< "${QUEUE_CONFIGS[$queue]}"
        total_cluster_memory=$((total_cluster_memory + queue_memory))
        total_cluster_vcores=$((total_cluster_vcores + queue_vcores))
    done

    echo "集群信息："
    echo "  - 节点数量: 641个"
    echo "  - 所有队列总内存: ${total_cluster_memory}GB"
    echo "  - 所有队列总vcores: ${total_cluster_vcores}"
    echo "  - 最大executor限制: 2564个（节点数×4）"
    echo ""
    
    for queue in "${!QUEUE_CONFIGS[@]}"; do
        local queue_config=${QUEUE_CONFIGS[$queue]}
        IFS=':' read -r queue_memory queue_vcores <<< "$queue_config"
        echo "队列: $queue (总内存: ${queue_memory}GB, 总vcores: ${queue_vcores})"

        # 显示不同资源级别的配置
        for level in 70 75 80 85 90; do
            local config
            config=$(calculate_executor_config "$queue_config" "$level")
            IFS=':' read -r num_executors executor_cores executor_memory used_memory used_vcores available_memory available_vcores <<< "$config"

            local target_memory=$((queue_memory * level / 100))
            local target_vcores=$((queue_vcores * level / 100))
            local memory_utilization=$((used_memory * 100 / queue_memory))
            local vcores_utilization=$((used_vcores * 100 / queue_vcores))
            local memory_efficiency=$((used_memory * 100 / target_memory))
            local vcores_efficiency=$((used_vcores * 100 / target_vcores))

            printf "  %s%%级别: %3d执行器 × %d核心 × %s = %4dGB内存/%4dvcores使用\n" \
                "$level" "$num_executors" "$executor_cores" "$executor_memory" "$used_memory" "$used_vcores"
            printf "           内存: %dGB/%dGB (利用率%d%%, 效率%d%%) | vcores: %d/%d (利用率%d%%, 效率%d%%)\n" \
                "$used_memory" "$queue_memory" "$memory_utilization" "$memory_efficiency" \
                "$used_vcores" "$queue_vcores" "$vcores_utilization" "$vcores_efficiency"
        done
        echo ""
    done
    
    echo "配置说明："
    echo "  - executor内存4-32GB，executor核心2-3个，适合641个节点的大型集群"
    echo "  - 同时考虑内存和CPU vcores约束，确保充分利用两种资源"
    echo "  - 效率接近100%表示资源配置最优"
    echo "  - 利用率显示实际使用资源占队列总资源的比例"
    echo "  - 所有配置均在YARN容器分配能力范围内"
}

# 命令行参数处理
case "${1:-help}" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        stop_service
        sleep 2
        start_service
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    config)
        show_queue_configs
        ;;
    cleanup)
        echo "手动清理所有YARN应用..."
        cleanup_remaining_applications
        ;;
    help|*)
        usage
        ;;
esac