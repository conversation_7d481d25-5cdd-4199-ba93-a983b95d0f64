---
- name: Check ChaosBlade Resource Manager Service Status
  hosts: chaosblade_nodes
  gather_facts: no
  become: yes
  become_user: root
  become_method: sudo
  vars:
    ansible_python_interpreter: /usr/bin/python3

  tasks:
    - name: Check service status
      systemd:
        name: chaosblade-resource-manager
      register: service_status
      failed_when: false

    - name: Report failed services
      debug:
        msg: "❌ {{ inventory_hostname }} - Service Status: {{ service_status.status.ActiveState | default('unknown') }}"
      when: service_status.status.ActiveState != 'active'
