---
- name: Deploy ChaosBlade Resource Manager
  hosts: chaosblade_nodes
  gather_facts: no
  become: yes
  become_user: root
  become_method: sudo
  vars:
    ansible_python_interpreter: /usr/bin/python3
    chaosblade_package: 'chaosblade_offline_package_v1.7.4.tar.gz'
    temp_dir: '/tmp/chaosblade_deploy'

  tasks:
    - name: Clean up existing temporary directory
      file:
        path: '{{ temp_dir }}'
        state: absent
      when: temp_dir != "/"

    - name: Create temporary directory
      file:
        path: '{{ temp_dir }}'
        state: directory
        mode: '0755'

    - name: Copy ChaosBlade package to target hosts
      copy:
        src: '{{ chaosblade_package }}'
        dest: '{{ temp_dir }}/{{ chaosblade_package }}'
        mode: '0644'

    - name: Extract ChaosBlade package
      unarchive:
        src: '{{ temp_dir }}/{{ chaosblade_package }}'
        dest: '{{ temp_dir }}'
        remote_src: yes

    - name: Run offline installation script
      command: ./install_offline.sh
      args:
        chdir: '{{ temp_dir }}/chaosblade_offline_package'

    - name: Clean up temporary files
      file:
        path: '{{ temp_dir }}'
        state: absent

    - name: Restart ChaosBlade Resource Manager service
      systemd:
        name: chaosblade-resource-manager
        state: restarted
        enabled: yes
        daemon_reload: yes

    - name: Wait for service to be active
      wait_for:
        timeout: 30
      delegate_to: localhost
      run_once: true

    - name: Verify service status
      systemd:
        name: chaosblade-resource-manager
      register: service_status

    - name: Show deployment summary
      debug:
        msg:
          - 'ChaosBlade Resource Manager deployed successfully!'
          - 'Service Status: {{ service_status.status.ActiveState }}'
          - 'Service Enabled: {{ service_status.status.UnitFileState }}'
          - 'Commands:'
          - '  Status: systemctl status chaosblade-resource-manager'
          - '  Logs: journalctl -u chaosblade-resource-manager -f'
          - '  Stop: systemctl stop chaosblade-resource-manager'
