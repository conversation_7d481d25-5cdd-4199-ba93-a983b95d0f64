# ChaosBlade 内网部署指南

## 文件准备

您需要将以下文件上传到内网环境：

1. **离线包**：`chaosblade_offline_package_v1.7.4.tar.gz`
2. **Ansible 配置目录**：`ansible/`

## 部署方式

### 方式一：单机部署

```bash
# 1. 解压离线包
tar -xzf chaosblade_offline_package_v1.7.4.tar.gz
cd chaosblade_offline_package

# 2. 执行安装
sudo ./install_offline.sh

# 3. 启动服务
sudo systemctl start chaosblade-resource-manager
sudo systemctl status chaosblade-resource-manager
```

### 方式二：多节点批量部署（推荐）

#### 1. 准备环境
```bash
# 在控制节点安装 Ansible
yum install -y ansible  # CentOS/RHEL
# 或
apt-get install -y ansible  # Ubuntu/Debian
```

#### 2. 上传文件
```bash
# 将以下文件上传到控制节点：
# - chaosblade_offline_package_v1.7.4.tar.gz
# - ansible/ 目录
```

#### 3. 配置主机清单
编辑 `ansible/inventory/hosts` 文件：
```ini
[chaosblade_nodes]
************
************
************

[chaosblade_nodes:vars]
ansible_user=root
ansible_ssh_common_args='-o StrictHostKeyChecking=no'
# 使用 SSH 密钥认证（已配置互信）
```

#### 4. 测试连接
```bash
cd ansible
ansible chaosblade_nodes -i inventory/hosts -m ping
```

#### 5. 执行批量部署
```bash
ansible-playbook -i inventory/hosts chaosblade-deploy.yml
```

#### 6. 检查服务状态
```bash
# 检查所有节点服务状态
ansible-playbook -i inventory/hosts chaosblade-check.yml
```

## 服务管理

### 系统服务命令
```bash
# 启动服务
systemctl start chaosblade-resource-manager

# 停止服务
systemctl stop chaosblade-resource-manager

# 查看状态
systemctl status chaosblade-resource-manager

# 查看日志
journalctl -u chaosblade-resource-manager -f

# 开机自启
systemctl enable chaosblade-resource-manager
```

### 直接命令
```bash
# 查看帮助
chaosblade_resource_manager.sh help

# 查看状态
chaosblade_resource_manager.sh status

# 配置管理
chaosblade_resource_manager.sh config

# 实时监控
chaosblade_resource_manager.sh monitor
```

## 重要文件位置

- **ChaosBlade 安装目录**：`/opt/chaosblade/`
- **工作目录**：`/opt/chaosblade-resource-manager/`
- **配置文件**：`/opt/chaosblade-resource-manager/config.conf`
- **日志文件**：`/opt/chaosblade-resource-manager/resource_manager.log`
- **脚本位置**：`/usr/local/bin/chaosblade_resource_manager.sh`
- **系统服务文件**：`/etc/systemd/system/chaosblade-resource-manager.service`

## 注意事项

1. **权限要求**：安装和运行都需要 root 权限
2. **网络要求**：确保 Ansible 控制节点能够 SSH 连接到目标节点
3. **防火墙**：确保必要的端口开放
4. **资源监控**：部署后建议监控系统资源使用情况
5. **备份**：重要数据请提前备份

## 故障排除

### 常见问题

1. **SSH 连接失败**
   - 检查主机清单配置
   - 确认 SSH 认证方式正确
   - 验证网络连通性

2. **服务启动失败**
   - 检查系统日志：`journalctl -u chaosblade-resource-manager`
   - 查看应用日志：`/opt/chaosblade-resource-manager/resource_manager.log`
   - 确认权限和依赖

3. **实验执行异常**
   - 检查 ChaosBlade 版本：`blade version`
   - 查看实验状态：`blade status`
   - 清理异常实验：`blade destroy [实验UID]`

### 手动清理

```bash
# 停止服务
systemctl stop chaosblade-resource-manager
systemctl disable chaosblade-resource-manager

# 清理系统服务
rm -f /etc/systemd/system/chaosblade-resource-manager.service
systemctl daemon-reload

# 清理 ChaosBlade 实验
blade destroy --all

# 清理文件
rm -rf /opt/chaosblade-resource-manager
rm -f /usr/local/bin/chaosblade_resource_manager.sh
```